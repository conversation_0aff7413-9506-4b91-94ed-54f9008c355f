import { UserProgress, UserPreferences, ActivitySession } from '@/types';

const STORAGE_KEYS = {
  USER_PROGRESS: 'impulse-shift-progress',
  USER_PREFERENCES: 'impulse-shift-preferences',
  ACTIVITY_SESSIONS: 'impulse-shift-sessions',
} as const;

export class StorageManager {
  private isClient = typeof window !== 'undefined';

  /**
   * Get user progress from localStorage
   */
  getUserProgress(): UserProgress {
    if (!this.isClient) {
      return this.getDefaultProgress();
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEYS.USER_PROGRESS);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading user progress:', error);
    }

    return this.getDefaultProgress();
  }

  /**
   * Save user progress to localStorage
   */
  saveUserProgress(progress: UserProgress): void {
    if (!this.isClient) return;

    try {
      localStorage.setItem(STORAGE_KEYS.USER_PROGRESS, JSON.stringify(progress));
    } catch (error) {
      console.error('Error saving user progress:', error);
    }
  }

  /**
   * Get user preferences from localStorage
   */
  getUserPreferences(): UserPreferences {
    if (!this.isClient) {
      return this.getDefaultPreferences();
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      if (stored) {
        const preferences = JSON.parse(stored);
        // Convert date strings back to Date objects
        preferences.completedActivities = preferences.completedActivities.map((session: any) => ({
          ...session,
          startTime: new Date(session.startTime),
          endTime: session.endTime ? new Date(session.endTime) : undefined,
        }));
        return preferences;
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }

    return this.getDefaultPreferences();
  }

  /**
   * Save user preferences to localStorage
   */
  saveUserPreferences(preferences: UserPreferences): void {
    if (!this.isClient) return;

    try {
      localStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
      console.error('Error saving user preferences:', error);
    }
  }

  /**
   * Add a completed activity session
   */
  addActivitySession(session: ActivitySession): void {
    const preferences = this.getUserPreferences();
    preferences.completedActivities.push(session);
    
    // Keep only last 100 sessions to prevent storage bloat
    if (preferences.completedActivities.length > 100) {
      preferences.completedActivities = preferences.completedActivities.slice(-100);
    }
    
    this.saveUserPreferences(preferences);
    
    // Update progress
    this.updateProgressFromSession(session);
  }

  /**
   * Update user progress based on completed session
   */
  private updateProgressFromSession(session: ActivitySession): void {
    const progress = this.getUserProgress();
    
    if (session.completed) {
      progress.totalCoins += session.coinsEarned;
      progress.totalActivitiesCompleted += 1;
      
      if (session.endTime && session.startTime) {
        const duration = (session.endTime.getTime() - session.startTime.getTime()) / (1000 * 60);
        progress.totalTimeShifted += duration;
      }
      
      // Update streak
      this.updateStreak(progress);
      
      // Update level (every 10 activities = 1 level)
      progress.level = Math.floor(progress.totalActivitiesCompleted / 10) + 1;
    }
    
    this.saveUserProgress(progress);
  }

  /**
   * Update streak based on recent activity
   */
  private updateStreak(progress: UserProgress): void {
    const preferences = this.getUserPreferences();
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // Check if user has activity today
    const hasActivityToday = preferences.completedActivities.some(session => {
      const sessionDate = new Date(session.startTime);
      sessionDate.setHours(0, 0, 0, 0);
      return sessionDate.getTime() === today.getTime() && session.completed;
    });
    
    // Check if user had activity yesterday
    const hasActivityYesterday = preferences.completedActivities.some(session => {
      const sessionDate = new Date(session.startTime);
      sessionDate.setHours(0, 0, 0, 0);
      return sessionDate.getTime() === yesterday.getTime() && session.completed;
    });
    
    if (hasActivityToday) {
      if (hasActivityYesterday || progress.currentStreak === 0) {
        progress.currentStreak += 1;
      }
    } else {
      progress.currentStreak = 0;
    }
    
    // Update longest streak
    if (progress.currentStreak > progress.longestStreak) {
      progress.longestStreak = progress.currentStreak;
    }
  }

  /**
   * Get default user progress
   */
  private getDefaultProgress(): UserProgress {
    return {
      totalCoins: 0,
      currentStreak: 0,
      longestStreak: 0,
      totalActivitiesCompleted: 0,
      totalTimeShifted: 0,
      achievements: [],
      level: 1,
    };
  }

  /**
   * Get default user preferences
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      favoriteActivities: [],
      skippedActivities: [],
      preferredMoods: [],
      preferredTimeSlots: [],
      completedActivities: [],
    };
  }

  /**
   * Clear all stored data (for testing/reset)
   */
  clearAllData(): void {
    if (!this.isClient) return;
    
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }
}

// Default instance
export const storageManager = new StorageManager();
