import { Activity, Mood, TimeSlot, UserPreferences } from '@/types';
import { activities } from '@/data/activities';

export class ActivitySelector {
  private activities: Activity[];
  private userPreferences: UserPreferences;

  constructor(userPreferences?: Partial<UserPreferences>) {
    this.activities = activities;
    this.userPreferences = {
      favoriteActivities: [],
      skippedActivities: [],
      preferredMoods: [],
      preferredTimeSlots: [],
      completedActivities: [],
      ...userPreferences
    };
  }

  /**
   * Select an activity based on mood, time, and user preferences
   */
  selectActivity(mood?: Mood, timeSlot?: TimeSlot): Activity | null {
    let filteredActivities = [...this.activities];

    // Filter by mood if provided
    if (mood) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.moods.includes(mood)
      );
    }

    // Filter by time slot if provided
    if (timeSlot) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.duration.includes(timeSlot)
      );
    }

    // Remove recently skipped activities
    filteredActivities = filteredActivities.filter(activity => 
      !this.userPreferences.skippedActivities.includes(activity.id)
    );

    // If no activities match, fall back to all activities
    if (filteredActivities.length === 0) {
      filteredActivities = [...this.activities];
    }

    // Prioritize favorite activities
    const favoriteActivities = filteredActivities.filter(activity => 
      this.userPreferences.favoriteActivities.includes(activity.id)
    );

    if (favoriteActivities.length > 0) {
      return this.getRandomActivity(favoriteActivities);
    }

    // Avoid recently completed activities for variety
    const recentActivityIds = this.userPreferences.completedActivities
      .slice(-5) // Last 5 activities
      .map(session => session.activityId);

    const freshActivities = filteredActivities.filter(activity => 
      !recentActivityIds.includes(activity.id)
    );

    if (freshActivities.length > 0) {
      return this.getRandomActivity(freshActivities);
    }

    // Fall back to any matching activity
    return this.getRandomActivity(filteredActivities);
  }

  /**
   * Get multiple activity suggestions
   */
  getSuggestions(mood?: Mood, timeSlot?: TimeSlot, count: number = 3): Activity[] {
    const suggestions: Activity[] = [];
    const usedIds = new Set<string>();

    for (let i = 0; i < count; i++) {
      const activity = this.selectActivity(mood, timeSlot);
      if (activity && !usedIds.has(activity.id)) {
        suggestions.push(activity);
        usedIds.add(activity.id);
      }
    }

    return suggestions;
  }

  /**
   * Get activities by category
   */
  getActivitiesByCategory(category: Activity['category']): Activity[] {
    return this.activities.filter(activity => activity.category === category);
  }

  /**
   * Update user preferences
   */
  updatePreferences(preferences: Partial<UserPreferences>): void {
    this.userPreferences = { ...this.userPreferences, ...preferences };
  }

  /**
   * Mark activity as favorite
   */
  addToFavorites(activityId: string): void {
    if (!this.userPreferences.favoriteActivities.includes(activityId)) {
      this.userPreferences.favoriteActivities.push(activityId);
    }
  }

  /**
   * Mark activity as skipped
   */
  addToSkipped(activityId: string): void {
    if (!this.userPreferences.skippedActivities.includes(activityId)) {
      this.userPreferences.skippedActivities.push(activityId);
    }
    
    // Remove from skipped after some time (keep only last 10)
    if (this.userPreferences.skippedActivities.length > 10) {
      this.userPreferences.skippedActivities = this.userPreferences.skippedActivities.slice(-10);
    }
  }

  private getRandomActivity(activities: Activity[]): Activity | null {
    if (activities.length === 0) return null;
    const randomIndex = Math.floor(Math.random() * activities.length);
    return activities[randomIndex];
  }
}

// Default instance
export const activitySelector = new ActivitySelector();
