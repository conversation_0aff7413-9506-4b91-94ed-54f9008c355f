'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { Mood, TimeSlot } from '@/types';

export default function Home() {
  const router = useRouter();
  const [showMoodSelector, setShowMoodSelector] = useState(false);
  const [showTimeSelector, setShowTimeSelector] = useState(false);
  const [selectedMood, setSelectedMood] = useState<Mood | null>(null);
  const [selectedTime, setSelectedTime] = useState<TimeSlot | null>(null);

  const moods: { value: Mood; label: string; icon: string; color: string }[] = [
    { value: 'energetic', label: 'Energetic', icon: '⚡', color: 'bg-yellow-500' },
    { value: 'calm', label: 'Calm', icon: '🌊', color: 'bg-blue-500' },
    { value: 'focused', label: 'Focused', icon: '🎯', color: 'bg-green-500' },
    { value: 'creative', label: 'Creative', icon: '🎨', color: 'bg-purple-500' },
    { value: 'stressed', label: 'Stressed', icon: '😤', color: 'bg-red-500' },
    { value: 'bored', label: 'Bored', icon: '😴', color: 'bg-gray-500' },
  ];

  const timeSlots: { value: TimeSlot; label: string }[] = [
    { value: 1, label: '1 min' },
    { value: 3, label: '3 min' },
    { value: 5, label: '5 min' },
    { value: 10, label: '10+ min' },
  ];

  const handleShiftNow = () => {
    if (!showMoodSelector && !showTimeSelector) {
      setShowMoodSelector(true);
      return;
    }

    if (showMoodSelector && !selectedMood) {
      return;
    }

    if (!showTimeSelector) {
      setShowTimeSelector(true);
      return;
    }

    if (!selectedTime) {
      return;
    }

    // Navigate to activity page
    const params = new URLSearchParams();
    if (selectedMood) params.set('mood', selectedMood);
    if (selectedTime) params.set('time', selectedTime.toString());

    router.push(`/activity?${params.toString()}`);
  };

  const resetSelection = () => {
    setShowMoodSelector(false);
    setShowTimeSelector(false);
    setSelectedMood(null);
    setSelectedTime(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex flex-col">
      {/* Header */}
      <header className="p-6 flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-white">ImpulseShift</h1>
        </div>

        <div className="flex items-center gap-4 text-white/80">
          <div className="flex items-center gap-2">
            <Trophy className="w-5 h-5" />
            <span className="text-sm">1,247 coins</span>
          </div>
          <div className="flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            <span className="text-sm">5 day streak</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex flex-col items-center justify-center p-8 text-center">
        <div className="max-w-2xl mx-auto">
          {/* Main Message */}
          <div className="mb-12">
            <h2 className="text-5xl font-bold text-white mb-4">
              Let's Shift Your Impulse
            </h2>
            <p className="text-xl text-white/80 mb-2">
              Transform mindless scrolling into something amazing
            </p>
            <p className="text-sm text-white/60">
              You've avoided <span className="font-semibold text-white">47 minutes</span> of doomscrolling this week! 🎉
            </p>
          </div>

          {/* Mood Selection */}
          {showMoodSelector && (
            <div className="mb-8 animate-in slide-in-from-bottom duration-300">
              <h3 className="text-2xl font-semibold text-white mb-6">How are you feeling?</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                {moods.map((mood) => (
                  <button
                    key={mood.value}
                    onClick={() => setSelectedMood(mood.value)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                      selectedMood === mood.value
                        ? 'border-white bg-white/20 scale-105'
                        : 'border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50'
                    }`}
                  >
                    <div className="text-3xl mb-2">{mood.icon}</div>
                    <div className="text-white font-medium">{mood.label}</div>
                  </button>
                ))}
              </div>
              <button
                onClick={() => setSelectedMood('focused')}
                className="text-white/60 hover:text-white/80 text-sm underline"
              >
                Skip - surprise me!
              </button>
            </div>
          )}

          {/* Time Selection */}
          {showTimeSelector && (
            <div className="mb-8 animate-in slide-in-from-bottom duration-300">
              <h3 className="text-2xl font-semibold text-white mb-6">How much time do you have?</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                {timeSlots.map((slot) => (
                  <button
                    key={slot.value}
                    onClick={() => setSelectedTime(slot.value)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                      selectedTime === slot.value
                        ? 'border-white bg-white/20 scale-105'
                        : 'border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50'
                    }`}
                  >
                    <Clock className="w-6 h-6 mx-auto mb-2 text-white" />
                    <div className="text-white font-medium">{slot.label}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Main Action Button */}
          <div className="mb-8">
            <button
              onClick={handleShiftNow}
              className="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-6 px-12 rounded-full text-2xl shadow-2xl transform hover:scale-105 transition-all duration-200 flex items-center gap-3 mx-auto"
            >
              <Zap className="w-8 h-8" />
              {!showMoodSelector ? 'Shift Now' :
               !showTimeSelector ? 'Continue' :
               'Start Activity'}
            </button>
          </div>

          {/* Reset/Back Button */}
          {(showMoodSelector || showTimeSelector) && (
            <button
              onClick={resetSelection}
              className="text-white/60 hover:text-white/80 text-sm underline"
            >
              Start over
            </button>
          )}

          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-6 mt-12 text-center">
            <div className="bg-white/10 rounded-xl p-4">
              <Brain className="w-8 h-8 mx-auto mb-2 text-white" />
              <div className="text-2xl font-bold text-white">127</div>
              <div className="text-sm text-white/60">Activities Done</div>
            </div>
            <div className="bg-white/10 rounded-xl p-4">
              <Heart className="w-8 h-8 mx-auto mb-2 text-white" />
              <div className="text-2xl font-bold text-white">5</div>
              <div className="text-sm text-white/60">Day Streak</div>
            </div>
            <div className="bg-white/10 rounded-xl p-4">
              <Clock className="w-8 h-8 mx-auto mb-2 text-white" />
              <div className="text-2xl font-bold text-white">2.3h</div>
              <div className="text-sm text-white/60">Time Shifted</div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="p-6 text-center">
        <p className="text-white/40 text-sm">
          "You're a Builder, not a Scroller" ✨
        </p>
      </footer>
    </div>
  );
}
