'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON>, Ski<PERSON><PERSON>or<PERSON>, <PERSON> } from 'lucide-react';
import { Activity, Mood, TimeSlot, ActivitySession } from '@/types';
import { activitySelector } from '@/utils/activitySelector';
import { storageManager } from '@/utils/storage';
import { activities } from '@/data/activities';

// Import activity components (we'll create these)
import PatternTap from '@/components/activities/PatternTap';
import BreathOrb from '@/components/activities/BreathOrb';
import SixtySecondScience from '@/components/activities/SixtySecondScience';
import GratitudeDrop from '@/components/activities/GratitudeDrop';

const activityComponents: Record<string, React.ComponentType<any>> = {
  PatternTap,
  BreathOrb,
  SixtySecondScience,
  GratitudeDrop,
};

export default function ActivityPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const [currentActivity, setCurrentActivity] = useState<Activity | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [session, setSession] = useState<ActivitySession | null>(null);
  const [showCompletion, setShowCompletion] = useState(false);

  useEffect(() => {
    const mood = searchParams.get('mood') as Mood;
    const timeSlot = parseInt(searchParams.get('time') || '3') as TimeSlot;
    const activityId = searchParams.get('id');

    let selectedActivity: Activity | null = null;

    if (activityId) {
      // Direct activity selection
      selectedActivity = activities.find(a => a.id === activityId) || null;
    } else {
      // Algorithm-based selection
      selectedActivity = activitySelector.selectActivity(mood, timeSlot);
    }

    if (selectedActivity) {
      setCurrentActivity(selectedActivity);
      
      // Create session
      const newSession: ActivitySession = {
        id: Date.now().toString(),
        activityId: selectedActivity.id,
        startTime: new Date(),
        mood: mood || undefined,
        timeSlot: timeSlot || 3,
        completed: false,
        coinsEarned: 0,
      };
      setSession(newSession);
    }

    setIsLoading(false);
  }, [searchParams]);

  const handleActivityComplete = (coinsEarned: number = 10) => {
    if (!session || !currentActivity) return;

    const completedSession: ActivitySession = {
      ...session,
      endTime: new Date(),
      completed: true,
      coinsEarned,
    };

    // Save to storage
    storageManager.addActivitySession(completedSession);
    
    setSession(completedSession);
    setShowCompletion(true);
  };

  const handleSkip = () => {
    if (currentActivity) {
      activitySelector.addToSkipped(currentActivity.id);
    }
    router.push('/');
  };

  const handleBackHome = () => {
    router.push('/');
  };

  const handleTryAnother = () => {
    // Get a new activity suggestion
    const mood = searchParams.get('mood') as Mood;
    const timeSlot = parseInt(searchParams.get('time') || '3') as TimeSlot;
    const newActivity = activitySelector.selectActivity(mood, timeSlot);
    
    if (newActivity) {
      router.push(`/activity?mood=${mood}&time=${timeSlot}&id=${newActivity.id}`);
    } else {
      router.push('/');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading your perfect activity...</div>
      </div>
    );
  }

  if (!currentActivity) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Oops! No activity found</h2>
          <button
            onClick={handleBackHome}
            className="bg-white/20 hover:bg-white/30 px-6 py-3 rounded-lg transition-colors"
          >
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  if (showCompletion && session?.completed) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center p-8">
        <div className="max-w-md mx-auto text-center">
          <div className="text-6xl mb-6">🎉</div>
          <h2 className="text-3xl font-bold text-white mb-4">Nice Shift!</h2>
          <p className="text-white/80 mb-2">
            You avoided {session.timeSlot} minutes of doomscrolling
          </p>
          <p className="text-white/60 mb-8">
            +{session.coinsEarned} coins earned!
          </p>
          
          <div className="space-y-4">
            <button
              onClick={handleTryAnother}
              className="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-3 px-6 rounded-lg transition-all"
            >
              Try Another Activity
            </button>
            <button
              onClick={handleBackHome}
              className="w-full bg-white/20 hover:bg-white/30 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  const ActivityComponent = activityComponents[currentActivity.component];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Header */}
      <header className="p-6 flex justify-between items-center">
        <button
          onClick={handleBackHome}
          className="flex items-center gap-2 text-white/80 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back</span>
        </button>
        
        <div className="text-center">
          <h1 className="text-xl font-bold text-white">{currentActivity.name}</h1>
          <p className="text-white/60 text-sm">{currentActivity.category}</p>
        </div>
        
        <button
          onClick={handleSkip}
          className="flex items-center gap-2 text-white/80 hover:text-white transition-colors"
        >
          <SkipForward className="w-5 h-5" />
          <span>Skip</span>
        </button>
      </header>

      {/* Activity Content */}
      <main className="flex-1 p-6">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white/10 rounded-2xl p-6 mb-6">
            <p className="text-white/80 text-center mb-6">
              {currentActivity.description}
            </p>
            
            {ActivityComponent ? (
              <ActivityComponent
                activity={currentActivity}
                onComplete={handleActivityComplete}
                timeSlot={session?.timeSlot || 3}
              />
            ) : (
              <div className="text-center text-white">
                <p className="mb-4">This activity is coming soon!</p>
                <button
                  onClick={() => handleActivityComplete(5)}
                  className="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-3 px-6 rounded-lg transition-all"
                >
                  Mark as Complete
                </button>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
