import { Activity, Achievement, MicrolearningContent, DrawingPrompt } from '@/types';

export const activities: Activity[] = [
  // Microgames
  {
    id: 'pattern-tap',
    name: '<PERSON>tern Tap',
    description: 'Remember and repeat the color sequence - like <PERSON> Says!',
    category: 'microgames',
    duration: [1, 3],
    moods: ['energetic', 'focused', 'bored'],
    component: 'PatternTap',
    difficulty: 'easy',
    tags: ['memory', 'quick', 'interactive']
  },
  {
    id: 'flash-focus',
    name: 'Flash Focus',
    description: 'Click the disappearing targets as fast as you can!',
    category: 'microgames',
    duration: [1, 3],
    moods: ['energetic', 'focused'],
    component: 'FlashFocus',
    difficulty: 'medium',
    tags: ['reaction', 'speed', 'focus']
  },
  {
    id: 'word-dash',
    name: 'Word Dash',
    description: 'Make as many words as possible from the given letters',
    category: 'microgames',
    duration: [3, 5],
    moods: ['focused', 'creative'],
    component: 'WordDash',
    difficulty: 'medium',
    tags: ['words', 'creative', 'thinking']
  },
  {
    id: 'reaction-spark',
    name: 'Reaction Spark',
    description: 'Test your reaction time and compete with yourself',
    category: 'microgames',
    duration: [1, 3],
    moods: ['energetic', 'focused'],
    component: 'ReactionSpark',
    difficulty: 'easy',
    tags: ['reaction', 'quick', 'competitive']
  },

  // Grounding Tools
  {
    id: 'breath-orb',
    name: 'Breath Orb',
    description: 'Follow the breathing orb to calm your mind',
    category: 'grounding',
    duration: [3, 5, 10],
    moods: ['stressed', 'calm'],
    component: 'BreathOrb',
    difficulty: 'easy',
    tags: ['breathing', 'meditation', 'calm']
  },
  {
    id: 'name-5-game',
    name: 'Name 5 Game',
    description: 'Ground yourself by naming 5 things you can see, hear, and feel',
    category: 'grounding',
    duration: [3, 5],
    moods: ['stressed', 'calm', 'focused'],
    component: 'Name5Game',
    difficulty: 'easy',
    tags: ['mindfulness', 'grounding', 'awareness']
  },
  {
    id: 'digital-aquarium',
    name: 'Digital Aquarium',
    description: 'Watch peaceful fish swim and feed them for relaxation',
    category: 'grounding',
    duration: [3, 5, 10],
    moods: ['stressed', 'calm', 'bored'],
    component: 'DigitalAquarium',
    difficulty: 'easy',
    tags: ['peaceful', 'visual', 'relaxing']
  },
  {
    id: 'ambient-escapes',
    name: 'Ambient Escapes',
    description: 'Immerse yourself in calming environments',
    category: 'grounding',
    duration: [5, 10],
    moods: ['stressed', 'calm'],
    component: 'AmbientEscapes',
    difficulty: 'easy',
    tags: ['ambient', 'nature', 'immersive']
  },

  // Microlearning
  {
    id: '60-second-science',
    name: '60-Second Science',
    description: 'Learn fascinating science facts in bite-sized pieces',
    category: 'microlearning',
    duration: [1, 3],
    moods: ['focused', 'creative', 'bored'],
    component: 'SixtySecondScience',
    difficulty: 'easy',
    tags: ['science', 'facts', 'learning']
  },
  {
    id: 'daily-wisdom',
    name: 'Daily Wisdom',
    description: 'Inspiring quotes and life lessons to motivate you',
    category: 'microlearning',
    duration: [1, 3],
    moods: ['focused', 'creative', 'stressed'],
    component: 'DailyWisdom',
    difficulty: 'easy',
    tags: ['inspiration', 'wisdom', 'motivation']
  },
  {
    id: 'skill-snap',
    name: 'Skill Snap',
    description: 'Quick productivity and life skill tips',
    category: 'microlearning',
    duration: [3, 5],
    moods: ['focused', 'creative'],
    component: 'SkillSnap',
    difficulty: 'medium',
    tags: ['productivity', 'skills', 'tips']
  },

  // Creative Prompts
  {
    id: 'draw-something-silly',
    name: 'Draw Something Silly',
    description: 'Express yourself with fun drawing prompts',
    category: 'creative',
    duration: [3, 5, 10],
    moods: ['creative', 'bored', 'energetic'],
    component: 'DrawSomethingSilly',
    difficulty: 'easy',
    tags: ['drawing', 'creative', 'fun']
  },
  {
    id: 'gratitude-drop',
    name: 'Gratitude Drop',
    description: 'Take a moment to appreciate the good things in life',
    category: 'creative',
    duration: [3, 5],
    moods: ['calm', 'stressed', 'focused'],
    component: 'GratitudeDrop',
    difficulty: 'easy',
    tags: ['gratitude', 'reflection', 'positive']
  },
  {
    id: 'free-write',
    name: 'Free Write',
    description: 'Let your thoughts flow freely onto the page',
    category: 'creative',
    duration: [3, 5, 10],
    moods: ['creative', 'stressed', 'focused'],
    component: 'FreeWrite',
    difficulty: 'easy',
    tags: ['writing', 'expression', 'thoughts']
  },
  {
    id: 'photo-prompt',
    name: 'Photo Prompt',
    description: 'Find beauty in everyday objects around you',
    category: 'creative',
    duration: [3, 5],
    moods: ['creative', 'bored'],
    component: 'PhotoPrompt',
    difficulty: 'easy',
    tags: ['photography', 'observation', 'creativity']
  }
];

export const achievements: Achievement[] = [
  {
    id: 'first-shift',
    name: 'First Shift',
    description: 'Complete your first activity',
    icon: '🌟',
    requirement: { type: 'activities', value: 1 }
  },
  {
    id: 'streak-starter',
    name: 'Streak Starter',
    description: 'Complete activities for 3 days in a row',
    icon: '🔥',
    requirement: { type: 'streak', value: 3 }
  },
  {
    id: 'week-warrior',
    name: 'Week Warrior',
    description: 'Complete activities for 7 days in a row',
    icon: '⚡',
    requirement: { type: 'streak', value: 7 }
  },
  {
    id: 'game-master',
    name: 'Game Master',
    description: 'Complete 10 microgame activities',
    icon: '🎮',
    requirement: { type: 'activities', value: 10, category: 'microgames' }
  },
  {
    id: 'zen-master',
    name: 'Zen Master',
    description: 'Complete 10 grounding activities',
    icon: '🧘',
    requirement: { type: 'activities', value: 10, category: 'grounding' }
  },
  {
    id: 'knowledge-seeker',
    name: 'Knowledge Seeker',
    description: 'Complete 10 microlearning activities',
    icon: '📚',
    requirement: { type: 'activities', value: 10, category: 'microlearning' }
  },
  {
    id: 'creative-soul',
    name: 'Creative Soul',
    description: 'Complete 10 creative activities',
    icon: '🎨',
    requirement: { type: 'activities', value: 10, category: 'creative' }
  },
  {
    id: 'time-saver',
    name: 'Time Saver',
    description: 'Save 60 minutes from mindless scrolling',
    icon: '⏰',
    requirement: { type: 'time', value: 60 }
  }
];
