// Core types for ImpulseShift app

export type Mood = 'energetic' | 'calm' | 'focused' | 'creative' | 'stressed' | 'bored';

export type TimeSlot = 1 | 3 | 5 | 10;

export type ActivityCategory = 'microgames' | 'grounding' | 'microlearning' | 'creative';

export interface Activity {
  id: string;
  name: string;
  description: string;
  category: ActivityCategory;
  duration: TimeSlot[];
  moods: Mood[];
  component: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  tags?: string[];
}

export interface UserPreferences {
  favoriteActivities: string[];
  skippedActivities: string[];
  preferredMoods: Mood[];
  preferredTimeSlots: TimeSlot[];
  completedActivities: ActivitySession[];
}

export interface ActivitySession {
  id: string;
  activityId: string;
  startTime: Date;
  endTime?: Date;
  mood?: Mood;
  timeSlot: TimeSlot;
  completed: boolean;
  rating?: number;
  coinsEarned: number;
}

export interface UserProgress {
  totalCoins: number;
  currentStreak: number;
  longestStreak: number;
  totalActivitiesCompleted: number;
  totalTimeShifted: number; // in minutes
  achievements: Achievement[];
  level: number;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt?: Date;
  requirement: {
    type: 'streak' | 'activities' | 'time' | 'category';
    value: number;
    category?: ActivityCategory;
  };
}

export interface GameState {
  score: number;
  timeRemaining: number;
  isActive: boolean;
  isCompleted: boolean;
  level?: number;
}

// Activity-specific interfaces
export interface PatternTapState extends GameState {
  sequence: number[];
  userSequence: number[];
  currentStep: number;
  showingSequence: boolean;
}

export interface FlashFocusState extends GameState {
  targets: { id: string; x: number; y: number; active: boolean }[];
  targetsHit: number;
}

export interface WordDashState extends GameState {
  letters: string[];
  foundWords: string[];
  currentWord: string;
}

export interface BreathingState {
  phase: 'inhale' | 'hold' | 'exhale' | 'pause';
  cycleCount: number;
  isActive: boolean;
  duration: number;
}

export interface DrawingPrompt {
  id: string;
  prompt: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface MicrolearningContent {
  id: string;
  title: string;
  content: string;
  type: 'fact' | 'tip' | 'quote' | 'question';
  category: string;
  estimatedReadTime: number;
}
