'use client';

import { useState, useEffect } from 'react';
import { Activity, BreathingState } from '@/types';

interface BreathOrbProps {
  activity: Activity;
  onComplete: (coinsEarned: number) => void;
  timeSlot: number;
}

export default function BreathOrb({ activity, onComplete, timeSlot }: BreathOrbProps) {
  const [breathingState, setBreathingState] = useState<BreathingState>({
    phase: 'inhale',
    cycleCount: 0,
    isActive: false,
    duration: timeSlot * 60, // Convert minutes to seconds
  });

  const [timeRemaining, setTimeRemaining] = useState(timeSlot * 60);
  const [phaseTimer, setPhaseTimer] = useState(0);

  // Breathing pattern: 4-4-4-4 (inhale-hold-exhale-pause)
  const phaseDurations = {
    inhale: 4,
    hold: 4,
    exhale: 4,
    pause: 4,
  };

  const phaseInstructions = {
    inhale: 'Breathe In',
    hold: 'Hold',
    exhale: 'Breathe Out',
    pause: 'Pause',
  };

  const phaseColors = {
    inhale: 'from-blue-400 to-cyan-400',
    hold: 'from-purple-400 to-blue-400',
    exhale: 'from-green-400 to-blue-400',
    pause: 'from-gray-400 to-gray-500',
  };

  // Start breathing session
  const startBreathing = () => {
    setBreathingState(prev => ({ ...prev, isActive: true }));
  };

  // Complete the session
  const completeSession = () => {
    setBreathingState(prev => ({ ...prev, isActive: false }));
    const coinsEarned = Math.max(8, Math.floor(breathingState.cycleCount / 2) * 3);
    onComplete(coinsEarned);
  };

  // Main timer effect
  useEffect(() => {
    if (breathingState.isActive && timeRemaining > 0) {
      const timer = setTimeout(() => {
        setTimeRemaining(prev => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (breathingState.isActive && timeRemaining <= 0) {
      completeSession();
    }
  }, [breathingState.isActive, timeRemaining]);

  // Phase timer effect
  useEffect(() => {
    if (breathingState.isActive) {
      const timer = setTimeout(() => {
        setPhaseTimer(prev => prev + 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [breathingState.isActive, phaseTimer]);

  // Phase transition effect
  useEffect(() => {
    if (breathingState.isActive) {
      const currentPhaseDuration = phaseDurations[breathingState.phase];
      
      if (phaseTimer >= currentPhaseDuration) {
        setPhaseTimer(0);
        
        setBreathingState(prev => {
          let nextPhase: BreathingState['phase'];
          let newCycleCount = prev.cycleCount;
          
          switch (prev.phase) {
            case 'inhale':
              nextPhase = 'hold';
              break;
            case 'hold':
              nextPhase = 'exhale';
              break;
            case 'exhale':
              nextPhase = 'pause';
              break;
            case 'pause':
              nextPhase = 'inhale';
              newCycleCount += 1;
              break;
          }
          
          return {
            ...prev,
            phase: nextPhase,
            cycleCount: newCycleCount,
          };
        });
      }
    }
  }, [phaseTimer, breathingState.phase, breathingState.isActive]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculate orb scale based on phase and timer
  const getOrbScale = () => {
    const progress = phaseTimer / phaseDurations[breathingState.phase];
    
    switch (breathingState.phase) {
      case 'inhale':
        return 0.5 + (progress * 0.5); // Scale from 0.5 to 1.0
      case 'hold':
        return 1.0; // Stay at full size
      case 'exhale':
        return 1.0 - (progress * 0.5); // Scale from 1.0 to 0.5
      case 'pause':
        return 0.5; // Stay at small size
      default:
        return 0.5;
    }
  };

  const orbScale = getOrbScale();

  return (
    <div className="text-center">
      {/* Session Stats */}
      <div className="flex justify-between items-center mb-6 text-white">
        <div>Cycles: {breathingState.cycleCount}</div>
        <div>Time: {formatTime(timeRemaining)}</div>
      </div>

      {/* Breathing Orb */}
      <div className="relative mb-8">
        <div className="flex items-center justify-center h-80">
          <div
            className={`
              w-64 h-64 rounded-full bg-gradient-to-br ${phaseColors[breathingState.phase]}
              transition-transform duration-1000 ease-in-out
              shadow-2xl
            `}
            style={{
              transform: `scale(${orbScale})`,
              filter: 'blur(1px)',
            }}
          />
          
          {/* Inner orb for extra effect */}
          <div
            className={`
              absolute w-48 h-48 rounded-full bg-gradient-to-br ${phaseColors[breathingState.phase]}
              transition-transform duration-1000 ease-in-out
              opacity-60
            `}
            style={{
              transform: `scale(${orbScale * 0.8})`,
            }}
          />
        </div>
      </div>

      {/* Phase Instruction */}
      <div className="text-white text-2xl font-semibold mb-4">
        {breathingState.isActive ? phaseInstructions[breathingState.phase] : 'Ready to breathe?'}
      </div>

      {/* Phase Progress */}
      {breathingState.isActive && (
        <div className="mb-6">
          <div className="w-32 h-2 bg-white/20 rounded-full mx-auto">
            <div
              className="h-full bg-white rounded-full transition-all duration-1000"
              style={{
                width: `${(phaseTimer / phaseDurations[breathingState.phase]) * 100}%`,
              }}
            />
          </div>
          <div className="text-white/60 text-sm mt-2">
            {phaseDurations[breathingState.phase] - phaseTimer}s
          </div>
        </div>
      )}

      {/* Start/Status */}
      {!breathingState.isActive && (
        <div>
          <button
            onClick={startBreathing}
            className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-bold py-3 px-8 rounded-lg transition-all transform hover:scale-105 mb-4"
          >
            Start Breathing
          </button>
          
          <div className="text-white/60 text-sm">
            <p>Follow the breathing orb to calm your mind.</p>
            <p>Inhale as it grows, exhale as it shrinks.</p>
          </div>
        </div>
      )}

      {breathingState.isActive && (
        <button
          onClick={completeSession}
          className="bg-white/20 hover:bg-white/30 text-white font-medium py-2 px-6 rounded-lg transition-colors"
        >
          Finish Early
        </button>
      )}
    </div>
  );
}
