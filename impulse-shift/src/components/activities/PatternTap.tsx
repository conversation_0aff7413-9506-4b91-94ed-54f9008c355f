'use client';

import { useState, useEffect, useCallback } from 'react';
import { Activity, PatternTapState } from '@/types';

interface PatternTapProps {
  activity: Activity;
  onComplete: (coinsEarned: number) => void;
  timeSlot: number;
}

const colors = [
  { id: 0, name: 'Red', bg: 'bg-red-500', hover: 'hover:bg-red-600' },
  { id: 1, name: 'Blue', bg: 'bg-blue-500', hover: 'hover:bg-blue-600' },
  { id: 2, name: 'Green', bg: 'bg-green-500', hover: 'hover:bg-green-600' },
  { id: 3, name: 'Yellow', bg: 'bg-yellow-500', hover: 'hover:bg-yellow-600' },
];

export default function PatternTap({ activity, onComplete, timeSlot }: PatternTapProps) {
  const [gameState, setGameState] = useState<PatternTapState>({
    score: 0,
    timeRemaining: timeSlot * 60, // Convert minutes to seconds
    isActive: false,
    isCompleted: false,
    sequence: [],
    userSequence: [],
    currentStep: 0,
    showingSequence: false,
  });

  const [message, setMessage] = useState('Ready to start?');
  const [activeColor, setActiveColor] = useState<number | null>(null);

  // Generate a new sequence
  const generateSequence = useCallback((length: number) => {
    const sequence = [];
    for (let i = 0; i < length; i++) {
      sequence.push(Math.floor(Math.random() * colors.length));
    }
    return sequence;
  }, []);

  // Start a new round
  const startNewRound = useCallback(() => {
    const sequenceLength = Math.min(3 + Math.floor(gameState.score / 2), 8);
    const newSequence = generateSequence(sequenceLength);
    
    setGameState(prev => ({
      ...prev,
      sequence: newSequence,
      userSequence: [],
      currentStep: 0,
      showingSequence: true,
    }));
    
    setMessage('Watch the pattern...');
    
    // Show sequence
    let index = 0;
    const showSequence = () => {
      if (index < newSequence.length) {
        setActiveColor(newSequence[index]);
        setTimeout(() => {
          setActiveColor(null);
          setTimeout(() => {
            index++;
            showSequence();
          }, 200);
        }, 600);
      } else {
        setGameState(prev => ({ ...prev, showingSequence: false }));
        setMessage('Now repeat the pattern!');
      }
    };
    
    setTimeout(showSequence, 500);
  }, [gameState.score, generateSequence]);

  // Handle color button click
  const handleColorClick = (colorId: number) => {
    if (gameState.showingSequence || gameState.isCompleted) return;
    
    const newUserSequence = [...gameState.userSequence, colorId];
    const currentIndex = gameState.userSequence.length;
    
    if (colorId === gameState.sequence[currentIndex]) {
      // Correct!
      setGameState(prev => ({ ...prev, userSequence: newUserSequence }));
      
      if (newUserSequence.length === gameState.sequence.length) {
        // Round completed!
        const newScore = gameState.score + 1;
        setGameState(prev => ({ ...prev, score: newScore }));
        setMessage(`Great! Score: ${newScore}`);
        
        setTimeout(() => {
          if (gameState.timeRemaining > 5) {
            startNewRound();
          } else {
            endGame();
          }
        }, 1000);
      }
    } else {
      // Wrong!
      setMessage('Oops! Try again...');
      setTimeout(() => {
        startNewRound();
      }, 1500);
    }
  };

  // Start the game
  const startGame = () => {
    setGameState(prev => ({
      ...prev,
      isActive: true,
      score: 0,
      timeRemaining: timeSlot * 60,
    }));
    startNewRound();
  };

  // End the game
  const endGame = () => {
    setGameState(prev => ({ ...prev, isActive: false, isCompleted: true }));
    setMessage(`Game Over! Final Score: ${gameState.score}`);
    
    // Calculate coins based on performance
    const coinsEarned = Math.max(5, gameState.score * 2);
    setTimeout(() => onComplete(coinsEarned), 2000);
  };

  // Timer effect
  useEffect(() => {
    if (gameState.isActive && gameState.timeRemaining > 0) {
      const timer = setTimeout(() => {
        setGameState(prev => ({ ...prev, timeRemaining: prev.timeRemaining - 1 }));
      }, 1000);
      return () => clearTimeout(timer);
    } else if (gameState.isActive && gameState.timeRemaining <= 0) {
      endGame();
    }
  }, [gameState.isActive, gameState.timeRemaining]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="text-center">
      {/* Game Stats */}
      <div className="flex justify-between items-center mb-6 text-white">
        <div>Score: {gameState.score}</div>
        <div>Time: {formatTime(gameState.timeRemaining)}</div>
      </div>

      {/* Message */}
      <div className="text-white text-lg mb-6 h-8">
        {message}
      </div>

      {/* Color Buttons */}
      <div className="grid grid-cols-2 gap-4 mb-6 max-w-xs mx-auto">
        {colors.map((color) => (
          <button
            key={color.id}
            onClick={() => handleColorClick(color.id)}
            disabled={gameState.showingSequence || gameState.isCompleted}
            className={`
              w-20 h-20 rounded-full transition-all duration-200 transform
              ${color.bg} ${color.hover}
              ${activeColor === color.id ? 'scale-110 ring-4 ring-white' : ''}
              ${gameState.showingSequence ? 'cursor-not-allowed opacity-70' : 'hover:scale-105'}
              disabled:cursor-not-allowed
            `}
          >
            <span className="sr-only">{color.name}</span>
          </button>
        ))}
      </div>

      {/* Start/Status */}
      {!gameState.isActive && !gameState.isCompleted && (
        <button
          onClick={startGame}
          className="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-3 px-8 rounded-lg transition-all transform hover:scale-105"
        >
          Start Game
        </button>
      )}

      {gameState.isCompleted && (
        <div className="text-white">
          <p className="text-xl font-bold mb-2">Well done! 🎉</p>
          <p className="text-white/80">Completing activity...</p>
        </div>
      )}

      {/* Instructions */}
      {!gameState.isActive && (
        <div className="mt-6 text-white/60 text-sm">
          <p>Watch the pattern, then repeat it by tapping the colors in order.</p>
          <p>Each correct sequence increases your score!</p>
        </div>
      )}
    </div>
  );
}
