'use client';

import { useState, useEffect } from 'react';
import { Activity } from '@/types';
import { BookO<PERSON>, Clock, CheckCircle } from 'lucide-react';

interface SixtySecondScienceProps {
  activity: Activity;
  onComplete: (coinsEarned: number) => void;
  timeSlot: number;
}

const scienceFacts = [
  {
    id: 1,
    title: "Why Do We Dream?",
    content: "Dreams occur during REM (Rapid Eye Movement) sleep when our brain is highly active. Scientists believe dreams help process emotions, consolidate memories, and solve problems. Your brain essentially 'rehearses' scenarios and strengthens neural pathways while you sleep. Interestingly, we forget about 95% of our dreams within minutes of waking up!",
    category: "Neuroscience",
    readTime: 45
  },
  {
    id: 2,
    title: "The Speed of Lightning",
    content: "Lightning is incredibly fast, but not instantaneous. The lightning bolt itself travels at about 1/3 the speed of light (100,000 km/s), while the thunder travels at the speed of sound (343 m/s). This is why you see lightning before hearing thunder. You can estimate how far lightning struck by counting seconds between flash and thunder, then dividing by 5 to get the distance in miles!",
    category: "Physics",
    readTime: 50
  },
  {
    id: 3,
    title: "Octopus Intelligence",
    content: "Octopuses have three hearts, blue blood, and are incredibly intelligent! They can solve puzzles, use tools, and even show personality traits. Each of their eight arms has its own 'brain' (nerve cluster) that can taste and smell independently. They're masters of camouflage, able to change both color and texture to match their surroundings in milliseconds.",
    category: "Biology",
    readTime: 40
  },
  {
    id: 4,
    title: "Black Holes and Time",
    content: "Near a black hole, time actually slows down due to extreme gravity - this is called time dilation. If you could survive near a black hole's event horizon for what feels like an hour to you, years might pass on Earth! This isn't science fiction - it's Einstein's theory of relativity in action. GPS satellites actually account for this effect to maintain accuracy.",
    category: "Astrophysics",
    readTime: 55
  },
  {
    id: 5,
    title: "Plant Communication",
    content: "Plants can 'talk' to each other! They release chemical signals through their roots and leaves to warn nearby plants of dangers like insect attacks. Some trees even share nutrients through underground fungal networks called 'mycorrhizae.' This 'wood wide web' allows forests to act almost like a single organism, with older trees nurturing younger ones.",
    category: "Botany",
    readTime: 48
  }
];

export default function SixtySecondScience({ activity, onComplete, timeSlot }: SixtySecondScienceProps) {
  const [currentFact, setCurrentFact] = useState(scienceFacts[Math.floor(Math.random() * scienceFacts.length)]);
  const [isReading, setIsReading] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  const [hasFinished, setHasFinished] = useState(false);

  const startReading = () => {
    setIsReading(true);
    setReadingProgress(0);
  };

  const finishReading = () => {
    setHasFinished(true);
    const coinsEarned = 8; // Fixed coins for learning activities
    setTimeout(() => onComplete(coinsEarned), 1500);
  };

  const getAnotherFact = () => {
    const availableFacts = scienceFacts.filter(fact => fact.id !== currentFact.id);
    const newFact = availableFacts[Math.floor(Math.random() * availableFacts.length)];
    setCurrentFact(newFact);
    setIsReading(false);
    setReadingProgress(0);
    setHasFinished(false);
  };

  // Simulate reading progress
  useEffect(() => {
    if (isReading && !hasFinished) {
      const interval = setInterval(() => {
        setReadingProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prev + (100 / currentFact.readTime); // Progress based on estimated read time
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isReading, hasFinished, currentFact.readTime]);

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-center gap-3 mb-6">
        <BookOpen className="w-6 h-6 text-white" />
        <h3 className="text-xl font-bold text-white">60-Second Science</h3>
      </div>

      {/* Fact Card */}
      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-6">
        {/* Fact Header */}
        <div className="flex items-center justify-between mb-4">
          <span className="bg-gradient-to-r from-pink-500 to-violet-500 text-white text-xs font-semibold px-3 py-1 rounded-full">
            {currentFact.category}
          </span>
          <div className="flex items-center gap-1 text-white/60 text-sm">
            <Clock className="w-4 h-4" />
            <span>{currentFact.readTime}s read</span>
          </div>
        </div>

        {/* Title */}
        <h4 className="text-2xl font-bold text-white mb-4">
          {currentFact.title}
        </h4>

        {/* Content */}
        <div className="text-white/90 leading-relaxed text-lg">
          {currentFact.content}
        </div>

        {/* Reading Progress */}
        {isReading && (
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-white/60 text-sm">Reading Progress</span>
              <span className="text-white/60 text-sm">{Math.round(readingProgress)}%</span>
            </div>
            <div className="w-full h-2 bg-white/20 rounded-full">
              <div
                className="h-full bg-gradient-to-r from-pink-500 to-violet-500 rounded-full transition-all duration-1000"
                style={{ width: `${readingProgress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="text-center space-y-4">
        {!isReading && !hasFinished && (
          <div>
            <button
              onClick={startReading}
              className="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-3 px-8 rounded-lg transition-all transform hover:scale-105 mb-4"
            >
              Start Reading
            </button>
            <div className="text-white/60 text-sm">
              <p>Expand your knowledge with fascinating science facts!</p>
            </div>
          </div>
        )}

        {isReading && readingProgress < 100 && (
          <div>
            <p className="text-white/80 mb-4">Take your time reading...</p>
            <button
              onClick={finishReading}
              className="bg-white/20 hover:bg-white/30 text-white font-medium py-2 px-6 rounded-lg transition-colors"
            >
              I'm Done Reading
            </button>
          </div>
        )}

        {isReading && readingProgress >= 100 && !hasFinished && (
          <div>
            <div className="flex items-center justify-center gap-2 text-green-400 mb-4">
              <CheckCircle className="w-5 h-5" />
              <span>Reading Complete!</span>
            </div>
            <div className="space-x-4">
              <button
                onClick={finishReading}
                className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-bold py-3 px-6 rounded-lg transition-all"
              >
                Finish Activity
              </button>
              <button
                onClick={getAnotherFact}
                className="bg-white/20 hover:bg-white/30 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                Read Another
              </button>
            </div>
          </div>
        )}

        {hasFinished && (
          <div className="text-center">
            <div className="text-4xl mb-4">🧠</div>
            <p className="text-xl font-bold text-white mb-2">Knowledge Gained!</p>
            <p className="text-white/80">Great job expanding your mind!</p>
          </div>
        )}
      </div>
    </div>
  );
}
