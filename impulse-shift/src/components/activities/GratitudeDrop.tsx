'use client';

import { useState } from 'react';
import { Activity } from '@/types';
import { Heart, Plus, Check } from 'lucide-react';

interface GratitudeDropProps {
  activity: Activity;
  onComplete: (coinsEarned: number) => void;
  timeSlot: number;
}

const gratitudePrompts = [
  "What made you smile today?",
  "Who are you thankful for right now?",
  "What's something beautiful you noticed recently?",
  "What skill or ability are you grateful to have?",
  "What's a small pleasure that brightened your day?",
  "What challenge helped you grow?",
  "What's something in nature you appreciate?",
  "What memory brings you joy?",
  "What opportunity are you grateful for?",
  "What comfort do you have that others might not?"
];

export default function GratitudeDrop({ activity, onComplete, timeSlot }: GratitudeDropProps) {
  const [gratitudeItems, setGratitudeItems] = useState<string[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [currentPrompt, setCurrentPrompt] = useState(
    gratitudePrompts[Math.floor(Math.random() * gratitudePrompts.length)]
  );
  const [isCompleted, setIsCompleted] = useState(false);

  const targetCount = 3; // Aim for 3 gratitude items

  const addGratitudeItem = () => {
    if (currentInput.trim()) {
      setGratitudeItems(prev => [...prev, currentInput.trim()]);
      setCurrentInput('');
      
      // Get a new prompt for the next item
      if (gratitudeItems.length + 1 < targetCount) {
        const availablePrompts = gratitudePrompts.filter(p => p !== currentPrompt);
        setCurrentPrompt(availablePrompts[Math.floor(Math.random() * availablePrompts.length)]);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      addGratitudeItem();
    }
  };

  const completeActivity = () => {
    setIsCompleted(true);
    const coinsEarned = Math.max(6, gratitudeItems.length * 3);
    setTimeout(() => onComplete(coinsEarned), 2000);
  };

  const removeItem = (index: number) => {
    setGratitudeItems(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-center gap-3 mb-6">
        <Heart className="w-6 h-6 text-pink-400" />
        <h3 className="text-xl font-bold text-white">Gratitude Drop</h3>
      </div>

      {/* Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-white/80 text-sm">Progress</span>
          <span className="text-white/80 text-sm">{gratitudeItems.length} / {targetCount}</span>
        </div>
        <div className="w-full h-2 bg-white/20 rounded-full">
          <div
            className="h-full bg-gradient-to-r from-pink-500 to-rose-500 rounded-full transition-all duration-500"
            style={{ width: `${(gratitudeItems.length / targetCount) * 100}%` }}
          />
        </div>
      </div>

      {/* Current Prompt */}
      {!isCompleted && gratitudeItems.length < targetCount && (
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-6">
          <h4 className="text-lg font-semibold text-white mb-4 text-center">
            {currentPrompt}
          </h4>
          
          <div className="space-y-4">
            <textarea
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your thoughts here..."
              className="w-full h-24 bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 resize-none focus:outline-none focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20"
              maxLength={200}
            />
            
            <div className="flex items-center justify-between">
              <span className="text-white/50 text-sm">
                {currentInput.length}/200 characters
              </span>
              <button
                onClick={addGratitudeItem}
                disabled={!currentInput.trim()}
                className="flex items-center gap-2 bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-all"
              >
                <Plus className="w-4 h-4" />
                Add
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Gratitude List */}
      {gratitudeItems.length > 0 && (
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Heart className="w-5 h-5 text-pink-400" />
            Your Gratitude List
          </h4>
          
          <div className="space-y-3">
            {gratitudeItems.map((item, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-start gap-3 animate-in slide-in-from-bottom duration-300"
              >
                <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  {index + 1}
                </div>
                <p className="text-white/90 flex-1">{item}</p>
                <button
                  onClick={() => removeItem(index)}
                  className="text-white/40 hover:text-white/80 transition-colors"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Completion */}
      {gratitudeItems.length >= targetCount && !isCompleted && (
        <div className="text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-6">
            <div className="text-4xl mb-4">✨</div>
            <h4 className="text-xl font-bold text-white mb-2">Beautiful!</h4>
            <p className="text-white/80 mb-4">
              You've captured {gratitudeItems.length} moments of gratitude. 
              Taking time to appreciate the good things in life can boost happiness and well-being.
            </p>
            
            <button
              onClick={completeActivity}
              className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white font-bold py-3 px-8 rounded-lg transition-all transform hover:scale-105"
            >
              Complete Activity
            </button>
          </div>
        </div>
      )}

      {isCompleted && (
        <div className="text-center">
          <div className="text-6xl mb-4">💖</div>
          <h4 className="text-2xl font-bold text-white mb-2">Gratitude Shared!</h4>
          <p className="text-white/80">
            Thank you for taking time to appreciate life's gifts.
          </p>
        </div>
      )}

      {/* Instructions */}
      {gratitudeItems.length === 0 && !isCompleted && (
        <div className="text-center text-white/60 text-sm">
          <p>Take a moment to reflect on the good things in your life.</p>
          <p>Research shows gratitude practice can improve mood and well-being!</p>
        </div>
      )}
    </div>
  );
}
