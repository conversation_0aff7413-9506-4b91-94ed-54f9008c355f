<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImpulseShift - Rewire Your Scroll</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes slideInFromBottom {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .slide-in {
            animation: slideInFromBottom 0.3s ease-out;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
    <div id="app">
        <!-- Header -->
        <header class="p-6 flex justify-between items-center">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-white">ImpulseShift</h1>
            </div>
            
            <div class="flex items-center gap-4 text-white/80">
                <div class="flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                    <span class="text-sm">1,247 coins</span>
                </div>
                <div class="flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                    <span class="text-sm">5 day streak</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 flex flex-col items-center justify-center p-8 text-center">
            <div class="max-w-2xl mx-auto">
                <!-- Main Message -->
                <div class="mb-12">
                    <h2 class="text-5xl font-bold text-white mb-4">
                        Let's Shift Your Impulse
                    </h2>
                    <p class="text-xl text-white/80 mb-2">
                        Transform mindless scrolling into something amazing
                    </p>
                    <p class="text-sm text-white/60">
                        You've avoided <span class="font-semibold text-white">47 minutes</span> of doomscrolling this week! 🎉
                    </p>
                </div>

                <!-- Mood Selection -->
                <div id="mood-selector" class="mb-8 hidden slide-in">
                    <h3 class="text-2xl font-semibold text-white mb-6">How are you feeling?</h3>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                        <button onclick="selectMood('energetic')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">⚡</div>
                            <div class="text-white font-medium">Energetic</div>
                        </button>
                        <button onclick="selectMood('calm')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">🌊</div>
                            <div class="text-white font-medium">Calm</div>
                        </button>
                        <button onclick="selectMood('focused')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">🎯</div>
                            <div class="text-white font-medium">Focused</div>
                        </button>
                        <button onclick="selectMood('creative')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">🎨</div>
                            <div class="text-white font-medium">Creative</div>
                        </button>
                        <button onclick="selectMood('stressed')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">😤</div>
                            <div class="text-white font-medium">Stressed</div>
                        </button>
                        <button onclick="selectMood('bored')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">😴</div>
                            <div class="text-white font-medium">Bored</div>
                        </button>
                    </div>
                    <button onclick="selectMood('focused')" class="text-white/60 hover:text-white/80 text-sm underline">
                        Skip - surprise me!
                    </button>
                </div>

                <!-- Time Selection -->
                <div id="time-selector" class="mb-8 hidden slide-in">
                    <h3 class="text-2xl font-semibold text-white mb-6">How much time do you have?</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <button onclick="selectTime(1)" class="time-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <svg class="w-6 h-6 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-white font-medium">1 min</div>
                        </button>
                        <button onclick="selectTime(3)" class="time-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <svg class="w-6 h-6 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-white font-medium">3 min</div>
                        </button>
                        <button onclick="selectTime(5)" class="time-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <svg class="w-6 h-6 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-white font-medium">5 min</div>
                        </button>
                        <button onclick="selectTime(10)" class="time-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <svg class="w-6 h-6 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-white font-medium">10+ min</div>
                        </button>
                    </div>
                </div>

                <!-- Main Action Button -->
                <div class="mb-8">
                    <button id="main-btn" onclick="handleShiftNow()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-6 px-12 rounded-full text-2xl shadow-2xl transform hover:scale-105 transition-all duration-200 flex items-center gap-3 mx-auto">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span id="btn-text">Shift Now</span>
                    </button>
                </div>

                <!-- Reset/Back Button -->
                <button id="reset-btn" onclick="resetSelection()" class="text-white/60 hover:text-white/80 text-sm underline hidden">
                    Start over
                </button>

                <!-- Quick Stats -->
                <div class="grid grid-cols-3 gap-6 mt-12 text-center">
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">127</div>
                        <div class="text-sm text-white/60">Activities Done</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">5</div>
                        <div class="text-sm text-white/60">Day Streak</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">2.3h</div>
                        <div class="text-sm text-white/60">Time Shifted</div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="p-6 text-center">
            <p class="text-white/40 text-sm">
                "You're a Builder, not a Scroller" ✨
            </p>
        </footer>
    </div>

    <script>
        let selectedMood = null;
        let selectedTime = null;
        let showMoodSelector = false;
        let showTimeSelector = false;

        function handleShiftNow() {
            if (!showMoodSelector && !showTimeSelector) {
                showMoodSelector = true;
                document.getElementById('mood-selector').classList.remove('hidden');
                document.getElementById('btn-text').textContent = 'Continue';
                document.getElementById('reset-btn').classList.remove('hidden');
                return;
            }
            
            if (showMoodSelector && !selectedMood) {
                return;
            }
            
            if (!showTimeSelector) {
                showTimeSelector = true;
                document.getElementById('time-selector').classList.remove('hidden');
                document.getElementById('btn-text').textContent = 'Start Activity';
                return;
            }
            
            if (!selectedTime) {
                return;
            }
            
            // Simulate starting activity
            startActivity();
        }

        function selectMood(mood) {
            selectedMood = mood;
            // Update UI to show selection
            document.querySelectorAll('.mood-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
            event.target.closest('.mood-btn').classList.add('border-white', 'bg-white/20', 'scale-105');
            event.target.closest('.mood-btn').classList.remove('border-white/30', 'bg-white/10');
        }

        function selectTime(time) {
            selectedTime = time;
            // Update UI to show selection
            document.querySelectorAll('.time-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
            event.target.closest('.time-btn').classList.add('border-white', 'bg-white/20', 'scale-105');
            event.target.closest('.time-btn').classList.remove('border-white/30', 'bg-white/10');
        }

        function resetSelection() {
            selectedMood = null;
            selectedTime = null;
            showMoodSelector = false;
            showTimeSelector = false;
            
            document.getElementById('mood-selector').classList.add('hidden');
            document.getElementById('time-selector').classList.add('hidden');
            document.getElementById('btn-text').textContent = 'Shift Now';
            document.getElementById('reset-btn').classList.add('hidden');
            
            // Reset button states
            document.querySelectorAll('.mood-btn, .time-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
        }

        // Pattern Tap Game Variables
        let gameState = {
            sequence: [],
            userSequence: [],
            score: 0,
            timeRemaining: 0,
            isActive: false,
            isCompleted: false,
            showingSequence: false,
            currentStep: 0
        };

        const colors = [
            { id: 0, name: 'Red', bg: 'bg-red-500', hover: 'hover:bg-red-600' },
            { id: 1, name: 'Blue', bg: 'bg-blue-500', hover: 'hover:bg-blue-600' },
            { id: 2, name: 'Green', bg: 'bg-green-500', hover: 'hover:bg-green-600' },
            { id: 3, name: 'Yellow', bg: 'bg-yellow-500', hover: 'hover:bg-yellow-600' }
        ];

        function startActivity() {
            gameState.timeRemaining = selectedTime * 60; // Convert minutes to seconds

            // Show Pattern Tap game interface
            document.querySelector('main').innerHTML = `
                <div class="flex-1 p-6">
                    <div class="max-w-2xl mx-auto">
                        <!-- Game Header -->
                        <div class="flex justify-between items-center mb-6">
                            <button onclick="location.reload()" class="flex items-center gap-2 text-white/80 hover:text-white transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                Back
                            </button>

                            <div class="text-center">
                                <h1 class="text-xl font-bold text-white">Pattern Tap</h1>
                                <p class="text-white/60 text-sm">microgames</p>
                            </div>

                            <button onclick="location.reload()" class="flex items-center gap-2 text-white/80 hover:text-white transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Skip
                            </button>
                        </div>

                        <!-- Game Content -->
                        <div class="bg-white/10 rounded-2xl p-6">
                            <p class="text-white/80 text-center mb-6">
                                Watch the pattern, then repeat it by tapping the colors in order!
                            </p>

                            <!-- Game Stats -->
                            <div class="flex justify-between items-center mb-6 text-white">
                                <div>Score: <span id="score">0</span></div>
                                <div>Time: <span id="timer">${formatTime(gameState.timeRemaining)}</span></div>
                            </div>

                            <!-- Message -->
                            <div class="text-white text-lg mb-6 h-8 text-center">
                                <span id="game-message">Ready to start?</span>
                            </div>

                            <!-- Color Buttons -->
                            <div class="grid grid-cols-2 gap-4 mb-6 max-w-xs mx-auto">
                                ${colors.map(color => `
                                    <button
                                        id="color-${color.id}"
                                        onclick="handleColorClick(${color.id})"
                                        class="color-btn w-20 h-20 rounded-full transition-all duration-200 transform ${color.bg} ${color.hover} hover:scale-105"
                                        disabled
                                    >
                                        <span class="sr-only">${color.name}</span>
                                    </button>
                                `).join('')}
                            </div>

                            <!-- Start/Status -->
                            <div class="text-center">
                                <button id="start-btn" onclick="startGame()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-3 px-8 rounded-lg transition-all transform hover:scale-105">
                                    Start Game
                                </button>

                                <div id="completion-message" class="hidden">
                                    <p class="text-xl font-bold mb-2 text-white">Well done! 🎉</p>
                                    <p class="text-white/80 mb-4">Final Score: <span id="final-score">0</span></p>
                                    <div class="space-y-3">
                                        <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-3 px-6 rounded-lg transition-all">
                                            Try Another Activity
                                        </button>
                                        <button onclick="location.reload()" class="w-full bg-white/20 hover:bg-white/30 text-white font-medium py-3 px-6 rounded-lg transition-colors">
                                            Back to Home
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Instructions -->
                            <div id="instructions" class="mt-6 text-white/60 text-sm text-center">
                                <p>Watch the pattern, then repeat it by tapping the colors in order.</p>
                                <p>Each correct sequence increases your score!</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Start the game timer
            startGameTimer();
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        function startGameTimer() {
            const timer = setInterval(() => {
                if (gameState.isActive && gameState.timeRemaining > 0) {
                    gameState.timeRemaining--;
                    document.getElementById('timer').textContent = formatTime(gameState.timeRemaining);
                } else if (gameState.isActive && gameState.timeRemaining <= 0) {
                    endGame();
                    clearInterval(timer);
                } else if (gameState.isCompleted) {
                    clearInterval(timer);
                }
            }, 1000);
        }

        function generateSequence(length) {
            const sequence = [];
            for (let i = 0; i < length; i++) {
                sequence.push(Math.floor(Math.random() * colors.length));
            }
            return sequence;
        }

        function startGame() {
            gameState.isActive = true;
            gameState.score = 0;
            gameState.isCompleted = false;

            document.getElementById('start-btn').style.display = 'none';
            document.getElementById('instructions').style.display = 'none';

            startNewRound();
        }

        function startNewRound() {
            const sequenceLength = Math.min(3 + Math.floor(gameState.score / 2), 8);
            gameState.sequence = generateSequence(sequenceLength);
            gameState.userSequence = [];
            gameState.currentStep = 0;
            gameState.showingSequence = true;

            document.getElementById('game-message').textContent = 'Watch the pattern...';

            // Disable color buttons during sequence display
            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.disabled = true;
                btn.classList.add('cursor-not-allowed', 'opacity-70');
            });

            // Show sequence
            showSequence();
        }

        function showSequence() {
            let index = 0;

            function showNextColor() {
                if (index < gameState.sequence.length) {
                    const colorId = gameState.sequence[index];
                    const colorBtn = document.getElementById(`color-${colorId}`);

                    // Flash the color
                    colorBtn.classList.add('scale-110', 'ring-4', 'ring-white');

                    setTimeout(() => {
                        colorBtn.classList.remove('scale-110', 'ring-4', 'ring-white');
                        setTimeout(() => {
                            index++;
                            showNextColor();
                        }, 200);
                    }, 600);
                } else {
                    // Sequence shown, now wait for user input
                    gameState.showingSequence = false;
                    document.getElementById('game-message').textContent = 'Now repeat the pattern!';

                    // Enable color buttons
                    document.querySelectorAll('.color-btn').forEach(btn => {
                        btn.disabled = false;
                        btn.classList.remove('cursor-not-allowed', 'opacity-70');
                    });
                }
            }

            setTimeout(showNextColor, 500);
        }

        function handleColorClick(colorId) {
            if (gameState.showingSequence || gameState.isCompleted) return;

            const currentIndex = gameState.userSequence.length;
            gameState.userSequence.push(colorId);

            // Visual feedback for user click
            const colorBtn = document.getElementById(`color-${colorId}`);
            colorBtn.classList.add('scale-110');
            setTimeout(() => colorBtn.classList.remove('scale-110'), 150);

            if (colorId === gameState.sequence[currentIndex]) {
                // Correct!
                if (gameState.userSequence.length === gameState.sequence.length) {
                    // Round completed!
                    gameState.score++;
                    document.getElementById('score').textContent = gameState.score;
                    document.getElementById('game-message').textContent = `Great! Score: ${gameState.score}`;

                    setTimeout(() => {
                        if (gameState.timeRemaining > 5) {
                            startNewRound();
                        } else {
                            endGame();
                        }
                    }, 1000);
                }
            } else {
                // Wrong!
                document.getElementById('game-message').textContent = 'Oops! Try again...';
                setTimeout(() => {
                    startNewRound();
                }, 1500);
            }
        }

        function endGame() {
            gameState.isActive = false;
            gameState.isCompleted = true;

            document.getElementById('game-message').textContent = `Game Over! Final Score: ${gameState.score}`;
            document.getElementById('final-score').textContent = gameState.score;

            // Disable all color buttons
            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.disabled = true;
                btn.classList.add('cursor-not-allowed', 'opacity-70');
            });

            // Show completion message
            setTimeout(() => {
                document.getElementById('completion-message').classList.remove('hidden');
            }, 2000);
        }
    </script>
</body>
</html>
