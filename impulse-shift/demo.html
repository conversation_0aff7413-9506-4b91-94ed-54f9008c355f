<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImpulseShift - Rewire Your Scroll</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes slideInFromBottom {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .slide-in {
            animation: slideInFromBottom 0.3s ease-out;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
    <div id="app">
        <!-- Header -->
        <header class="p-6 flex justify-between items-center">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-white">ImpulseShift</h1>
            </div>
            
            <div class="flex items-center gap-4 text-white/80">
                <div class="flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                    <span class="text-sm">1,247 coins</span>
                </div>
                <div class="flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                    <span class="text-sm">5 day streak</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 flex flex-col items-center justify-center p-8 text-center">
            <div class="max-w-2xl mx-auto">
                <!-- Main Message -->
                <div class="mb-12">
                    <h2 class="text-5xl font-bold text-white mb-4">
                        Let's Shift Your Impulse
                    </h2>
                    <p class="text-xl text-white/80 mb-2">
                        Transform mindless scrolling into something amazing
                    </p>
                    <p class="text-sm text-white/60">
                        You've avoided <span class="font-semibold text-white">47 minutes</span> of doomscrolling this week! 🎉
                    </p>
                </div>

                <!-- Mood Selection -->
                <div id="mood-selector" class="mb-8 hidden slide-in">
                    <h3 class="text-2xl font-semibold text-white mb-6">How are you feeling?</h3>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                        <button onclick="selectMood('energetic')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">⚡</div>
                            <div class="text-white font-medium">Energetic</div>
                        </button>
                        <button onclick="selectMood('calm')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">🌊</div>
                            <div class="text-white font-medium">Calm</div>
                        </button>
                        <button onclick="selectMood('focused')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">🎯</div>
                            <div class="text-white font-medium">Focused</div>
                        </button>
                        <button onclick="selectMood('creative')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">🎨</div>
                            <div class="text-white font-medium">Creative</div>
                        </button>
                        <button onclick="selectMood('stressed')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">😤</div>
                            <div class="text-white font-medium">Stressed</div>
                        </button>
                        <button onclick="selectMood('bored')" class="mood-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <div class="text-3xl mb-2">😴</div>
                            <div class="text-white font-medium">Bored</div>
                        </button>
                    </div>
                    <button onclick="selectMood('focused')" class="text-white/60 hover:text-white/80 text-sm underline">
                        Skip - surprise me!
                    </button>
                </div>

                <!-- Time Selection -->
                <div id="time-selector" class="mb-8 hidden slide-in">
                    <h3 class="text-2xl font-semibold text-white mb-6">How much time do you have?</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <button onclick="selectTime(1)" class="time-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <svg class="w-6 h-6 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-white font-medium">1 min</div>
                        </button>
                        <button onclick="selectTime(3)" class="time-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <svg class="w-6 h-6 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-white font-medium">3 min</div>
                        </button>
                        <button onclick="selectTime(5)" class="time-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <svg class="w-6 h-6 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-white font-medium">5 min</div>
                        </button>
                        <button onclick="selectTime(10)" class="time-btn p-4 rounded-xl border-2 border-white/30 bg-white/10 hover:bg-white/20 hover:border-white/50 transition-all duration-200">
                            <svg class="w-6 h-6 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-white font-medium">10+ min</div>
                        </button>
                    </div>
                </div>

                <!-- Main Action Button -->
                <div class="mb-8">
                    <button id="main-btn" onclick="handleShiftNow()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-6 px-12 rounded-full text-2xl shadow-2xl transform hover:scale-105 transition-all duration-200 flex items-center gap-3 mx-auto">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span id="btn-text">Shift Now</span>
                    </button>
                </div>

                <!-- Reset/Back Button -->
                <button id="reset-btn" onclick="resetSelection()" class="text-white/60 hover:text-white/80 text-sm underline hidden">
                    Start over
                </button>

                <!-- Quick Stats -->
                <div class="grid grid-cols-3 gap-6 mt-12 text-center">
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">127</div>
                        <div class="text-sm text-white/60">Activities Done</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">5</div>
                        <div class="text-sm text-white/60">Day Streak</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">2.3h</div>
                        <div class="text-sm text-white/60">Time Shifted</div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="p-6 text-center">
            <p class="text-white/40 text-sm">
                "You're a Builder, not a Scroller" ✨
            </p>
        </footer>
    </div>

    <script>
        let selectedMood = null;
        let selectedTime = null;
        let showMoodSelector = false;
        let showTimeSelector = false;

        function handleShiftNow() {
            if (!showMoodSelector && !showTimeSelector) {
                showMoodSelector = true;
                document.getElementById('mood-selector').classList.remove('hidden');
                document.getElementById('btn-text').textContent = 'Continue';
                document.getElementById('reset-btn').classList.remove('hidden');
                return;
            }
            
            if (showMoodSelector && !selectedMood) {
                return;
            }
            
            if (!showTimeSelector) {
                showTimeSelector = true;
                document.getElementById('time-selector').classList.remove('hidden');
                document.getElementById('btn-text').textContent = 'Start Activity';
                return;
            }
            
            if (!selectedTime) {
                return;
            }
            
            // Simulate starting activity
            startActivity();
        }

        function selectMood(mood) {
            selectedMood = mood;
            // Update UI to show selection
            document.querySelectorAll('.mood-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
            event.target.closest('.mood-btn').classList.add('border-white', 'bg-white/20', 'scale-105');
            event.target.closest('.mood-btn').classList.remove('border-white/30', 'bg-white/10');
        }

        function selectTime(time) {
            selectedTime = time;
            // Update UI to show selection
            document.querySelectorAll('.time-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
            event.target.closest('.time-btn').classList.add('border-white', 'bg-white/20', 'scale-105');
            event.target.closest('.time-btn').classList.remove('border-white/30', 'bg-white/10');
        }

        function resetSelection() {
            selectedMood = null;
            selectedTime = null;
            showMoodSelector = false;
            showTimeSelector = false;
            
            document.getElementById('mood-selector').classList.add('hidden');
            document.getElementById('time-selector').classList.add('hidden');
            document.getElementById('btn-text').textContent = 'Shift Now';
            document.getElementById('reset-btn').classList.add('hidden');
            
            // Reset button states
            document.querySelectorAll('.mood-btn, .time-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
        }

        function startActivity() {
            // Show completion message
            document.querySelector('main').innerHTML = `
                <div class="flex items-center justify-center min-h-[60vh]">
                    <div class="max-w-md mx-auto text-center">
                        <div class="text-6xl mb-6 animate-pulse-slow">🎉</div>
                        <h2 class="text-3xl font-bold text-white mb-4">Activity Started!</h2>
                        <p class="text-white/80 mb-2">
                            Mood: <span class="capitalize font-semibold">${selectedMood}</span>
                        </p>
                        <p class="text-white/80 mb-8">
                            Time: <span class="font-semibold">${selectedTime} minutes</span>
                        </p>
                        
                        <div class="space-y-4">
                            <div class="bg-white/10 rounded-lg p-4">
                                <p class="text-white/90">
                                    🎮 <strong>Pattern Tap</strong> - Remember and repeat the color sequence!
                                </p>
                                <p class="text-white/60 text-sm mt-2">
                                    A fun memory game to challenge your brain and improve focus.
                                </p>
                            </div>
                            
                            <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-3 px-6 rounded-lg transition-all">
                                Try Another Activity
                            </button>
                            <button onclick="location.reload()" class="w-full bg-white/20 hover:bg-white/30 text-white font-medium py-3 px-6 rounded-lg transition-colors">
                                Back to Home
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
