<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImpulseShift - Rewire Your Scroll</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'shimmer': 'shimmer 2s linear infinite',
                        'bounce-gentle': 'bounceGentle 2s ease-in-out infinite',
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.4s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'wiggle': 'wiggle 1s ease-in-out infinite',
                        'particle': 'particle 3s ease-out infinite',
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(236, 72, 153, 0.5); }
            to { box-shadow: 0 0 30px rgba(236, 72, 153, 0.8), 0 0 40px rgba(139, 92, 246, 0.3); }
        }
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        @keyframes bounceGentle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }
        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-3deg); }
            75% { transform: rotate(3deg); }
        }
        @keyframes particle {
            0% { transform: translateY(0) scale(1); opacity: 1; }
            100% { transform: translateY(-100px) scale(0); opacity: 0; }
        }

        .slide-in { animation: slideInFromBottom 0.3s ease-out; }
        .glass { backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.1); }
        .gradient-text { background: linear-gradient(135deg, #ec4899, #8b5cf6, #06b6d4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
        .shimmer-bg { background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent); background-size: 200% 100%; }
        .particle { position: absolute; width: 4px; height: 4px; background: radial-gradient(circle, #ec4899, #8b5cf6); border-radius: 50%; pointer-events: none; }

        /* Custom scrollbar */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: rgba(255, 255, 255, 0.1); border-radius: 4px; }
        ::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #ec4899, #8b5cf6); border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #db2777, #7c3aed); }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-x-hidden">
    <!-- Animated Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-pink-500/20 to-violet-500/20 rounded-full blur-xl animate-float"></div>
        <div class="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-xl animate-float" style="animation-delay: -2s;"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-gradient-to-r from-violet-500/20 to-pink-500/20 rounded-full blur-xl animate-float" style="animation-delay: -4s;"></div>
        <div class="absolute bottom-20 right-1/3 w-28 h-28 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded-full blur-xl animate-float" style="animation-delay: -1s;"></div>
    </div>

    <div id="app" class="relative z-10">
        <!-- Header -->
        <header class="p-6 flex justify-between items-center animate-fade-in">
            <div class="flex items-center gap-3 group">
                <div class="w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 animate-glow">
                    <svg class="w-7 h-7 text-white animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h1 class="text-3xl font-bold gradient-text animate-shimmer shimmer-bg">ImpulseShift</h1>
            </div>

            <div class="flex items-center gap-6 text-white/90">
                <div class="flex items-center gap-2 glass rounded-full px-4 py-2 hover:bg-white/10 transition-all duration-300 cursor-pointer group">
                    <svg class="w-5 h-5 text-yellow-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                    <span class="text-sm font-semibold">1,247</span>
                    <span class="text-xs text-white/60">coins</span>
                </div>
                <div class="flex items-center gap-2 glass rounded-full px-4 py-2 hover:bg-white/10 transition-all duration-300 cursor-pointer group">
                    <svg class="w-5 h-5 text-orange-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                    <span class="text-sm font-semibold">5</span>
                    <span class="text-xs text-white/60">day streak</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 flex flex-col items-center justify-center p-8 text-center">
            <div class="max-w-4xl mx-auto">
                <!-- Main Message -->
                <div class="mb-16 animate-slide-up">
                    <div class="relative">
                        <h2 class="text-6xl md:text-7xl font-black text-white mb-6 leading-tight">
                            Let's <span class="gradient-text animate-shimmer shimmer-bg">Shift</span> Your Impulse
                        </h2>
                        <!-- Floating particles around the title -->
                        <div class="absolute -top-4 left-1/4 particle animate-particle" style="animation-delay: 0s;"></div>
                        <div class="absolute -top-2 right-1/3 particle animate-particle" style="animation-delay: 1s;"></div>
                        <div class="absolute top-8 left-1/3 particle animate-particle" style="animation-delay: 2s;"></div>
                    </div>
                    <p class="text-2xl text-white/90 mb-4 font-medium">
                        Transform mindless scrolling into something <span class="text-pink-300 font-semibold">amazing</span>
                    </p>
                    <div class="glass rounded-2xl p-6 mb-4 hover:bg-white/10 transition-all duration-300 cursor-pointer group">
                        <p class="text-lg text-white/70 group-hover:text-white/90 transition-colors">
                            You've avoided <span class="font-bold text-emerald-300 text-xl">47 minutes</span> of doomscrolling this week!
                            <span class="inline-block animate-bounce-gentle">🎉</span>
                        </p>
                        <div class="mt-2 w-full bg-white/20 rounded-full h-2">
                            <div class="bg-gradient-to-r from-emerald-400 to-cyan-400 h-2 rounded-full w-3/4 animate-shimmer shimmer-bg"></div>
                        </div>
                    </div>
                </div>

                <!-- Mood Selection -->
                <div id="mood-selector" class="mb-12 hidden animate-slide-up">
                    <h3 class="text-3xl font-bold text-white mb-8 gradient-text">How are you feeling?</h3>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-6 mb-8">
                        <button onclick="selectMood('energetic')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-yellow-500/20 hover:to-orange-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">⚡</div>
                            <div class="text-white font-semibold text-lg">Energetic</div>
                            <div class="text-white/60 text-sm mt-1">Ready to conquer!</div>
                        </button>
                        <button onclick="selectMood('calm')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-blue-500/20 hover:to-cyan-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">🌊</div>
                            <div class="text-white font-semibold text-lg">Calm</div>
                            <div class="text-white/60 text-sm mt-1">Peaceful vibes</div>
                        </button>
                        <button onclick="selectMood('focused')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-green-500/20 hover:to-emerald-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">🎯</div>
                            <div class="text-white font-semibold text-lg">Focused</div>
                            <div class="text-white/60 text-sm mt-1">Laser sharp</div>
                        </button>
                        <button onclick="selectMood('creative')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-purple-500/20 hover:to-pink-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">🎨</div>
                            <div class="text-white font-semibold text-lg">Creative</div>
                            <div class="text-white/60 text-sm mt-1">Artistic flow</div>
                        </button>
                        <button onclick="selectMood('stressed')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-red-500/20 hover:to-pink-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">😤</div>
                            <div class="text-white font-semibold text-lg">Stressed</div>
                            <div class="text-white/60 text-sm mt-1">Need relief</div>
                        </button>
                        <button onclick="selectMood('bored')" class="mood-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-gray-500/20 hover:to-slate-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="text-4xl mb-3 group-hover:animate-bounce-gentle">😴</div>
                            <div class="text-white font-semibold text-lg">Bored</div>
                            <div class="text-white/60 text-sm mt-1">Need excitement</div>
                        </button>
                    </div>
                    <button onclick="selectMood('focused')" class="glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 font-medium">
                        ✨ Skip - surprise me!
                    </button>
                </div>

                <!-- Time Selection -->
                <div id="time-selector" class="mb-12 hidden animate-slide-up">
                    <h3 class="text-3xl font-bold text-white mb-8 gradient-text">How much time do you have?</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                        <button onclick="selectTime(1)" class="time-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-emerald-500/20 hover:to-teal-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="relative">
                                <svg class="w-8 h-8 mx-auto mb-3 text-emerald-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-white font-semibold text-lg">1 min</div>
                            <div class="text-white/60 text-sm mt-1">Quick boost</div>
                        </button>
                        <button onclick="selectTime(3)" class="time-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-blue-500/20 hover:to-cyan-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="relative">
                                <svg class="w-8 h-8 mx-auto mb-3 text-blue-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-white font-semibold text-lg">3 min</div>
                            <div class="text-white/60 text-sm mt-1">Perfect break</div>
                        </button>
                        <button onclick="selectTime(5)" class="time-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-purple-500/20 hover:to-pink-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="relative">
                                <svg class="w-8 h-8 mx-auto mb-3 text-purple-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="absolute -top-1 -right-1 w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-white font-semibold text-lg">5 min</div>
                            <div class="text-white/60 text-sm mt-1">Deep dive</div>
                        </button>
                        <button onclick="selectTime(10)" class="time-btn group p-6 rounded-2xl glass hover:bg-gradient-to-br hover:from-orange-500/20 hover:to-red-500/20 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                            <div class="relative">
                                <svg class="w-8 h-8 mx-auto mb-3 text-orange-400 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="absolute -top-1 -right-1 w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-white font-semibold text-lg">10+ min</div>
                            <div class="text-white/60 text-sm mt-1">Full experience</div>
                        </button>
                    </div>
                </div>

                <!-- Main Action Button -->
                <div class="mb-8">
                    <button id="main-btn" onclick="handleShiftNow()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-6 px-12 rounded-full text-2xl shadow-2xl transform hover:scale-105 transition-all duration-200 flex items-center gap-3 mx-auto">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span id="btn-text">Shift Now</span>
                    </button>
                </div>

                <!-- Reset/Back Button -->
                <button id="reset-btn" onclick="resetSelection()" class="text-white/60 hover:text-white/80 text-sm underline hidden">
                    Start over
                </button>

                <!-- Quick Stats -->
                <div class="grid grid-cols-3 gap-6 mt-12 text-center">
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">127</div>
                        <div class="text-sm text-white/60">Activities Done</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">5</div>
                        <div class="text-sm text-white/60">Day Streak</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-4">
                        <svg class="w-8 h-8 mx-auto mb-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-2xl font-bold text-white">2.3h</div>
                        <div class="text-sm text-white/60">Time Shifted</div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="p-6 text-center">
            <p class="text-white/40 text-sm">
                "You're a Builder, not a Scroller" ✨
            </p>
        </footer>
    </div>

    <script>
        let selectedMood = null;
        let selectedTime = null;
        let showMoodSelector = false;
        let showTimeSelector = false;

        function handleShiftNow() {
            if (!showMoodSelector && !showTimeSelector) {
                showMoodSelector = true;
                document.getElementById('mood-selector').classList.remove('hidden');
                document.getElementById('btn-text').textContent = 'Continue';
                document.getElementById('reset-btn').classList.remove('hidden');
                return;
            }
            
            if (showMoodSelector && !selectedMood) {
                return;
            }
            
            if (!showTimeSelector) {
                showTimeSelector = true;
                document.getElementById('time-selector').classList.remove('hidden');
                document.getElementById('btn-text').textContent = 'Start Activity';
                return;
            }
            
            if (!selectedTime) {
                return;
            }
            
            // Simulate starting activity
            startActivity();
        }

        function selectMood(mood) {
            selectedMood = mood;
            // Update UI to show selection
            document.querySelectorAll('.mood-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
            event.target.closest('.mood-btn').classList.add('border-white', 'bg-white/20', 'scale-105');
            event.target.closest('.mood-btn').classList.remove('border-white/30', 'bg-white/10');
        }

        function selectTime(time) {
            selectedTime = time;
            // Update UI to show selection
            document.querySelectorAll('.time-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
            event.target.closest('.time-btn').classList.add('border-white', 'bg-white/20', 'scale-105');
            event.target.closest('.time-btn').classList.remove('border-white/30', 'bg-white/10');
        }

        function resetSelection() {
            selectedMood = null;
            selectedTime = null;
            showMoodSelector = false;
            showTimeSelector = false;
            
            document.getElementById('mood-selector').classList.add('hidden');
            document.getElementById('time-selector').classList.add('hidden');
            document.getElementById('btn-text').textContent = 'Shift Now';
            document.getElementById('reset-btn').classList.add('hidden');
            
            // Reset button states
            document.querySelectorAll('.mood-btn, .time-btn').forEach(btn => {
                btn.classList.remove('border-white', 'bg-white/20', 'scale-105');
                btn.classList.add('border-white/30', 'bg-white/10');
            });
        }

        // Comprehensive Activity Database
        const activities = {
            bored: [
                {
                    id: 'odd-one-out',
                    name: 'Odd One Out',
                    description: 'Quickly tap the image that doesn\'t belong!',
                    icon: '🔍',
                    category: 'Visual Puzzle',
                    difficulty: 'Easy'
                },
                {
                    id: 'would-you-rather',
                    name: 'Would You Rather?',
                    description: 'Funny or deep rapid-fire choices!',
                    icon: '🤔',
                    category: 'Decision Game',
                    difficulty: 'Easy'
                },
                {
                    id: 'emoji-decode',
                    name: 'Emoji Decode',
                    description: 'Guess the phrase from emoji clues!',
                    icon: '😎',
                    category: 'Word Game',
                    difficulty: 'Medium'
                },
                {
                    id: 'color-clash',
                    name: 'Color Clash',
                    description: 'Word says "Blue" but text is red - choose the actual color!',
                    icon: '🌈',
                    category: 'Brain Teaser',
                    difficulty: 'Medium'
                },
                {
                    id: 'pattern-tap',
                    name: 'Pattern Tap',
                    description: 'Remember and repeat the color sequence!',
                    icon: '🎮',
                    category: 'Memory Game',
                    difficulty: 'Medium'
                },
                {
                    id: 'mini-music-mixer',
                    name: 'Mini Music Mixer',
                    description: 'Layer beats and loops to create your tune!',
                    icon: '🎵',
                    category: 'Creative',
                    difficulty: 'Easy'
                },
                {
                    id: 'tap-to-zap',
                    name: 'Tap to Zap',
                    description: 'Bug zapping game with increasing speed!',
                    icon: '⚡',
                    category: 'Action Game',
                    difficulty: 'Hard'
                }
            ],
            stressed: [
                {
                    id: 'box-breathing',
                    name: 'Box Breathing Guide',
                    description: 'Animated square to help inhale/hold/exhale',
                    icon: '🫁',
                    category: 'Breathing',
                    difficulty: 'Easy'
                },
                {
                    id: 'nature-sounds',
                    name: 'Nature Sound Scroller',
                    description: 'Swipe through forests, rain, wind, etc.',
                    icon: '🌲',
                    category: 'Relaxation',
                    difficulty: 'Easy'
                },
                {
                    id: 'trace-path',
                    name: 'Trace the Path',
                    description: 'Slowly trace a glowing spiral with your finger',
                    icon: '🌀',
                    category: 'Mindfulness',
                    difficulty: 'Easy'
                },
                {
                    id: 'feeling-wheel',
                    name: 'Name That Feeling',
                    description: 'Spin a wheel of emotions and describe what fits',
                    icon: '🎡',
                    category: 'Emotional',
                    difficulty: 'Medium'
                },
                {
                    id: 'grounding-5-4-3',
                    name: '5-4-3-2-1 Grounding',
                    description: 'Guided sensory grounding exercise',
                    icon: '🧘',
                    category: 'Mindfulness',
                    difficulty: 'Easy'
                }
            ],
            energetic: [
                {
                    id: 'reaction-spark',
                    name: 'Reaction Spark',
                    description: 'Test your lightning-fast reflexes!',
                    icon: '⚡',
                    category: 'Speed Game',
                    difficulty: 'Medium'
                },
                {
                    id: 'word-dash',
                    name: 'Word Dash',
                    description: 'Make as many words as possible from letters!',
                    icon: '📝',
                    category: 'Word Game',
                    difficulty: 'Medium'
                },
                {
                    id: 'flash-focus',
                    name: 'Flash Focus',
                    description: 'Click disappearing targets as fast as you can!',
                    icon: '🎯',
                    category: 'Focus Game',
                    difficulty: 'Hard'
                }
            ],
            creative: [
                {
                    id: 'draw-monster',
                    name: 'Draw-a-Monster',
                    description: 'Doodle and name your own silly monster!',
                    icon: '👹',
                    category: 'Drawing',
                    difficulty: 'Easy'
                },
                {
                    id: 'improv-prompt',
                    name: 'Improv Prompt Generator',
                    description: 'Invent a product using random objects!',
                    icon: '💡',
                    category: 'Creative Thinking',
                    difficulty: 'Medium'
                }
            ],
            focused: [
                {
                    id: 'micro-learning',
                    name: '60-Second Science',
                    description: 'Learn fascinating facts in bite-sized pieces!',
                    icon: '🧠',
                    category: 'Learning',
                    difficulty: 'Easy'
                },
                {
                    id: 'productivity-bingo',
                    name: 'Productivity Bingo',
                    description: 'Cross off microtasks to build momentum!',
                    icon: '✅',
                    category: 'Productivity',
                    difficulty: 'Easy'
                }
            ],
            calm: [
                {
                    id: 'gratitude-drop',
                    name: 'Gratitude Drop',
                    description: 'Take a moment to appreciate life\'s gifts',
                    icon: '💖',
                    category: 'Reflection',
                    difficulty: 'Easy'
                },
                {
                    id: 'zen-garden',
                    name: 'Mini Zen Garden',
                    description: 'Drag patterns through virtual sand',
                    icon: '🏯',
                    category: 'Meditation',
                    difficulty: 'Easy'
                }
            ]
        };

        // Game state variables
        let gameState = {
            sequence: [],
            userSequence: [],
            score: 0,
            timeRemaining: 0,
            isActive: false,
            isCompleted: false,
            showingSequence: false,
            currentStep: 0
        };

        const colors = [
            { id: 0, name: 'Red', bg: 'bg-red-500', hover: 'hover:bg-red-600' },
            { id: 1, name: 'Blue', bg: 'bg-blue-500', hover: 'hover:bg-blue-600' },
            { id: 2, name: 'Green', bg: 'bg-green-500', hover: 'hover:bg-green-600' },
            { id: 3, name: 'Yellow', bg: 'bg-yellow-500', hover: 'hover:bg-yellow-600' }
        ];

        function getRandomActivity(mood) {
            const moodActivities = activities[mood] || activities['focused'];
            return moodActivities[Math.floor(Math.random() * moodActivities.length)];
        }

        function startActivity() {
            const selectedActivity = getRandomActivity(selectedMood);

            // Show beautiful activity selection interface
            document.querySelector('main').innerHTML = `
                <div class="flex-1 p-6 animate-fade-in">
                    <div class="max-w-4xl mx-auto">
                        <!-- Activity Header -->
                        <div class="flex justify-between items-center mb-8">
                            <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                <span class="font-medium">Back Home</span>
                            </button>

                            <div class="text-center">
                                <div class="text-4xl mb-2 animate-bounce-gentle">${selectedActivity.icon}</div>
                                <h1 class="text-2xl font-bold text-white">${selectedActivity.name}</h1>
                                <p class="text-white/60 text-sm">${selectedActivity.category} • ${selectedActivity.difficulty}</p>
                            </div>

                            <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span class="font-medium">Try Another</span>
                            </button>
                        </div>

                        <!-- Activity Card -->
                        <div class="glass rounded-3xl p-8 mb-8 hover:bg-white/10 transition-all duration-500 group">
                            <div class="text-center mb-8">
                                <div class="text-6xl mb-4 group-hover:animate-bounce-gentle">${selectedActivity.icon}</div>
                                <h2 class="text-3xl font-bold text-white mb-3 gradient-text">${selectedActivity.name}</h2>
                                <p class="text-xl text-white/80 mb-6">${selectedActivity.description}</p>

                                <!-- Activity Stats -->
                                <div class="flex justify-center gap-6 mb-8">
                                    <div class="glass rounded-2xl px-6 py-3">
                                        <div class="text-2xl font-bold text-white">${selectedTime}</div>
                                        <div class="text-white/60 text-sm">minutes</div>
                                    </div>
                                    <div class="glass rounded-2xl px-6 py-3">
                                        <div class="text-2xl font-bold text-emerald-400">+15</div>
                                        <div class="text-white/60 text-sm">coins</div>
                                    </div>
                                    <div class="glass rounded-2xl px-6 py-3">
                                        <div class="text-2xl font-bold text-purple-400">${selectedActivity.difficulty}</div>
                                        <div class="text-white/60 text-sm">level</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Start Button -->
                            <div class="text-center">
                                <button onclick="startSpecificActivity('${selectedActivity.id}')" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-12 rounded-full text-xl shadow-2xl transform hover:scale-105 transition-all duration-300 animate-glow group">
                                    <div class="flex items-center gap-3">
                                        <svg class="w-6 h-6 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.5a2.5 2.5 0 110 5H9V10z"></path>
                                        </svg>
                                        <span>Start ${selectedActivity.name}</span>
                                        <svg class="w-6 h-6 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                </button>
                            </div>
                        </div>

                        <!-- Mood-based Suggestions -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-white mb-6 text-center">More ${selectedMood.charAt(0).toUpperCase() + selectedMood.slice(1)} Activities</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                ${activities[selectedMood].slice(0, 3).map(activity => `
                                    <div class="glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 cursor-pointer group" onclick="startSpecificActivity('${activity.id}')">
                                        <div class="text-center">
                                            <div class="text-3xl mb-3 group-hover:animate-bounce-gentle">${activity.icon}</div>
                                            <h4 class="text-white font-semibold mb-2">${activity.name}</h4>
                                            <p class="text-white/60 text-sm">${activity.description}</p>
                                            <div class="mt-3 text-xs text-white/50">${activity.category}</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function startSpecificActivity(activityId) {
            if (activityId === 'pattern-tap') {
                startPatternTapGame();
            } else {
                // For other activities, show a coming soon message with beautiful UI
                showComingSoon(activityId);
            }
        }

        function showComingSoon(activityId) {
            const activity = Object.values(activities).flat().find(a => a.id === activityId);

            document.querySelector('main').innerHTML = `
                <div class="flex-1 flex items-center justify-center p-8">
                    <div class="max-w-2xl mx-auto text-center animate-scale-in">
                        <div class="glass rounded-3xl p-12">
                            <div class="text-8xl mb-6 animate-bounce-gentle">${activity.icon}</div>
                            <h2 class="text-4xl font-bold text-white mb-4 gradient-text">${activity.name}</h2>
                            <p class="text-xl text-white/80 mb-8">${activity.description}</p>

                            <div class="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-2xl p-6 mb-8">
                                <div class="text-4xl mb-3">🚀</div>
                                <h3 class="text-2xl font-bold text-white mb-2">Coming Soon!</h3>
                                <p class="text-white/70">This amazing activity is being crafted with love. Try our Pattern Tap game for now!</p>
                            </div>

                            <div class="space-y-4">
                                <button onclick="startSpecificActivity('pattern-tap')" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                    🎮 Try Pattern Tap Instead
                                </button>
                                <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                    🏠 Back to Home
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function startPatternTapGame() {
            gameState.timeRemaining = selectedTime * 60; // Convert minutes to seconds

            // Show enhanced Pattern Tap game interface
            document.querySelector('main').innerHTML = `
                <div class="flex-1 p-6 animate-fade-in">
                    <div class="max-w-4xl mx-auto">
                        <!-- Enhanced Game Header -->
                        <div class="flex justify-between items-center mb-8">
                            <button onclick="location.reload()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-bounce-gentle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                <span class="font-medium">Back Home</span>
                            </button>

                            <div class="text-center">
                                <div class="text-4xl mb-2 animate-bounce-gentle">🎮</div>
                                <h1 class="text-3xl font-bold gradient-text">Pattern Tap</h1>
                                <p class="text-white/60">Memory Challenge • Medium</p>
                            </div>

                            <button onclick="startActivity()" class="flex items-center gap-3 glass rounded-full px-6 py-3 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-300 group">
                                <svg class="w-5 h-5 group-hover:animate-wiggle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span class="font-medium">New Game</span>
                            </button>
                        </div>

                        <!-- Enhanced Game Content -->
                        <div class="glass rounded-3xl p-8 mb-8">
                            <!-- Game Description -->
                            <div class="text-center mb-8">
                                <p class="text-xl text-white/90 mb-4">
                                    🧠 <strong>Challenge your memory!</strong> Watch the pattern, then repeat it perfectly.
                                </p>
                                <div class="flex justify-center gap-4 text-sm text-white/60">
                                    <span>🎯 Focus</span>
                                    <span>⚡ Speed</span>
                                    <span>🧩 Memory</span>
                                </div>
                            </div>

                            <!-- Enhanced Game Stats -->
                            <div class="grid grid-cols-3 gap-6 mb-8">
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-white">
                                        <span id="score">0</span>
                                    </div>
                                    <div class="text-white/60 text-sm">Score</div>
                                </div>
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-emerald-400">
                                        <span id="timer">${formatTime(gameState.timeRemaining)}</span>
                                    </div>
                                    <div class="text-white/60 text-sm">Time Left</div>
                                </div>
                                <div class="glass rounded-2xl p-4 text-center">
                                    <div class="text-2xl font-bold text-purple-400">
                                        <span id="level">1</span>
                                    </div>
                                    <div class="text-white/60 text-sm">Level</div>
                                </div>
                            </div>

                            <!-- Enhanced Message Display -->
                            <div class="text-center mb-8">
                                <div class="glass rounded-2xl p-6">
                                    <div class="text-2xl font-bold text-white mb-2">
                                        <span id="game-message">Ready to challenge your memory?</span>
                                    </div>
                                    <div class="text-white/60">
                                        <span id="game-subtitle">Tap Start Game when you're ready!</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Color Buttons -->
                            <div class="grid grid-cols-2 gap-6 mb-8 max-w-md mx-auto">
                                ${colors.map(color => `
                                    <button
                                        id="color-${color.id}"
                                        onclick="handleColorClick(${color.id})"
                                        class="color-btn w-24 h-24 rounded-2xl transition-all duration-300 transform ${color.bg} ${color.hover} hover:scale-110 shadow-2xl glass border-2 border-white/20 hover:border-white/40 disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled
                                    >
                                        <span class="sr-only">${color.name}</span>
                                        <div class="w-full h-full rounded-2xl bg-gradient-to-br from-white/20 to-transparent"></div>
                                    </button>
                                `).join('')}
                            </div>

                            <!-- Enhanced Start/Status Section -->
                            <div class="text-center">
                                <button id="start-btn" onclick="startGame()" class="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform hover:scale-105 transition-all duration-300 animate-glow">
                                    <div class="flex items-center gap-3">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.5a2.5 2.5 0 110 5H9V10z"></path>
                                        </svg>
                                        <span>Start Game</span>
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                </button>

                                <div id="completion-message" class="hidden animate-scale-in">
                                    <div class="glass rounded-3xl p-8 mt-8">
                                        <div class="text-6xl mb-4">🎉</div>
                                        <h3 class="text-3xl font-bold text-white mb-4">Incredible Memory!</h3>
                                        <p class="text-xl text-white/80 mb-2">Final Score: <span id="final-score" class="text-emerald-400 font-bold">0</span></p>
                                        <p class="text-white/60 mb-8">You've earned <span class="text-yellow-400 font-semibold">+25 coins</span>!</p>
                                        <div class="space-y-4">
                                            <button onclick="startGame()" class="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                                🎮 Play Again
                                            </button>
                                            <button onclick="location.reload()" class="w-full bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105">
                                                🎯 Try Another Activity
                                            </button>
                                            <button onclick="location.reload()" class="w-full glass hover:bg-white/10 text-white font-medium py-4 px-8 rounded-2xl transition-all">
                                                🏠 Back to Home
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Instructions -->
                            <div id="instructions" class="mt-8 glass rounded-2xl p-6 text-center">
                                <h4 class="text-lg font-bold text-white mb-3">How to Play</h4>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-white/70">
                                    <div>
                                        <div class="text-2xl mb-2">👀</div>
                                        <p><strong>Watch</strong> the colors light up in sequence</p>
                                    </div>
                                    <div>
                                        <div class="text-2xl mb-2">👆</div>
                                        <p><strong>Repeat</strong> by tapping the same pattern</p>
                                    </div>
                                    <div>
                                        <div class="text-2xl mb-2">🚀</div>
                                        <p><strong>Level up</strong> with longer sequences!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Start the game timer
            startGameTimer();
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        function startGameTimer() {
            const timer = setInterval(() => {
                if (gameState.isActive && gameState.timeRemaining > 0) {
                    gameState.timeRemaining--;
                    document.getElementById('timer').textContent = formatTime(gameState.timeRemaining);
                } else if (gameState.isActive && gameState.timeRemaining <= 0) {
                    endGame();
                    clearInterval(timer);
                } else if (gameState.isCompleted) {
                    clearInterval(timer);
                }
            }, 1000);
        }

        function generateSequence(length) {
            const sequence = [];
            for (let i = 0; i < length; i++) {
                sequence.push(Math.floor(Math.random() * colors.length));
            }
            return sequence;
        }

        function startGame() {
            gameState.isActive = true;
            gameState.score = 0;
            gameState.isCompleted = false;

            document.getElementById('start-btn').style.display = 'none';
            document.getElementById('instructions').style.display = 'none';

            startNewRound();
        }

        function startNewRound() {
            const sequenceLength = Math.min(3 + Math.floor(gameState.score / 2), 8);
            const level = Math.floor(gameState.score / 3) + 1;

            gameState.sequence = generateSequence(sequenceLength);
            gameState.userSequence = [];
            gameState.currentStep = 0;
            gameState.showingSequence = true;

            // Update UI with enhanced messaging
            document.getElementById('game-message').textContent = `Level ${level} - Watch carefully!`;
            document.getElementById('game-subtitle').textContent = `Sequence length: ${sequenceLength} colors`;
            document.getElementById('level').textContent = level;

            // Enhanced button styling during sequence display
            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.disabled = true;
                btn.classList.add('cursor-not-allowed');
                btn.classList.remove('hover:scale-110');
                btn.style.transform = 'scale(0.95)';
                btn.style.opacity = '0.6';
            });

            // Show sequence with enhanced effects
            showSequence();
        }

        function showSequence() {
            let index = 0;

            function showNextColor() {
                if (index < gameState.sequence.length) {
                    const colorId = gameState.sequence[index];
                    const colorBtn = document.getElementById(`color-${colorId}`);

                    // Enhanced flash effect with multiple animations
                    colorBtn.style.transform = 'scale(1.2)';
                    colorBtn.style.opacity = '1';
                    colorBtn.classList.add('ring-4', 'ring-white', 'shadow-2xl');
                    colorBtn.style.boxShadow = '0 0 30px rgba(255, 255, 255, 0.8), 0 0 60px rgba(255, 255, 255, 0.4)';

                    // Add particle effect
                    createParticleEffect(colorBtn);

                    setTimeout(() => {
                        colorBtn.style.transform = 'scale(0.95)';
                        colorBtn.style.opacity = '0.6';
                        colorBtn.classList.remove('ring-4', 'ring-white', 'shadow-2xl');
                        colorBtn.style.boxShadow = '';

                        setTimeout(() => {
                            index++;
                            showNextColor();
                        }, 300);
                    }, 800);
                } else {
                    // Sequence shown, now wait for user input
                    gameState.showingSequence = false;
                    document.getElementById('game-message').textContent = 'Your turn! Repeat the pattern';
                    document.getElementById('game-subtitle').textContent = 'Tap the colors in the same order';

                    // Re-enable color buttons with enhanced styling
                    document.querySelectorAll('.color-btn').forEach(btn => {
                        btn.disabled = false;
                        btn.classList.remove('cursor-not-allowed');
                        btn.classList.add('hover:scale-110');
                        btn.style.transform = 'scale(1)';
                        btn.style.opacity = '1';
                    });
                }
            }

            setTimeout(showNextColor, 800);
        }

        function createParticleEffect(element) {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            for (let i = 0; i < 8; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle animate-particle';
                particle.style.left = centerX + 'px';
                particle.style.top = centerY + 'px';
                particle.style.animationDelay = (i * 0.1) + 's';

                const angle = (i / 8) * Math.PI * 2;
                const distance = 50;
                particle.style.setProperty('--end-x', Math.cos(angle) * distance + 'px');
                particle.style.setProperty('--end-y', Math.sin(angle) * distance + 'px');

                document.body.appendChild(particle);

                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 3000);
            }
        }

        function handleColorClick(colorId) {
            if (gameState.showingSequence || gameState.isCompleted) return;

            const currentIndex = gameState.userSequence.length;
            gameState.userSequence.push(colorId);

            // Enhanced visual feedback for user click
            const colorBtn = document.getElementById(`color-${colorId}`);
            colorBtn.style.transform = 'scale(1.15)';
            colorBtn.classList.add('ring-2', 'ring-white');

            // Create click particle effect
            createParticleEffect(colorBtn);

            setTimeout(() => {
                colorBtn.style.transform = 'scale(1)';
                colorBtn.classList.remove('ring-2', 'ring-white');
            }, 200);

            if (colorId === gameState.sequence[currentIndex]) {
                // Correct! Show positive feedback
                colorBtn.style.boxShadow = '0 0 20px rgba(34, 197, 94, 0.8)';
                setTimeout(() => colorBtn.style.boxShadow = '', 500);

                if (gameState.userSequence.length === gameState.sequence.length) {
                    // Round completed!
                    gameState.score++;
                    const level = Math.floor(gameState.score / 3) + 1;

                    document.getElementById('score').textContent = gameState.score;
                    document.getElementById('level').textContent = level;
                    document.getElementById('game-message').textContent = `🎉 Perfect! Level ${level}`;
                    document.getElementById('game-subtitle').textContent = `Score: ${gameState.score} • Keep going!`;

                    // Show celebration effect
                    showCelebrationEffect();

                    setTimeout(() => {
                        if (gameState.timeRemaining > 5) {
                            startNewRound();
                        } else {
                            endGame();
                        }
                    }, 1500);
                } else {
                    // Partial success
                    document.getElementById('game-message').textContent = `✅ Correct! ${gameState.userSequence.length}/${gameState.sequence.length}`;
                    document.getElementById('game-subtitle').textContent = 'Keep going...';
                }
            } else {
                // Wrong! Show negative feedback
                colorBtn.style.boxShadow = '0 0 20px rgba(239, 68, 68, 0.8)';
                setTimeout(() => colorBtn.style.boxShadow = '', 500);

                document.getElementById('game-message').textContent = '❌ Oops! Starting over...';
                document.getElementById('game-subtitle').textContent = 'Don\'t worry, you\'ve got this!';

                // Shake effect for wrong answer
                document.querySelector('.glass').style.animation = 'wiggle 0.5s ease-in-out';
                setTimeout(() => {
                    document.querySelector('.glass').style.animation = '';
                }, 500);

                setTimeout(() => {
                    startNewRound();
                }, 2000);
            }
        }

        function showCelebrationEffect() {
            // Create multiple celebration particles
            const gameArea = document.querySelector('.glass');
            const rect = gameArea.getBoundingClientRect();

            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                particle.innerHTML = ['🎉', '✨', '🌟', '💫', '⭐'][Math.floor(Math.random() * 5)];
                particle.style.position = 'fixed';
                particle.style.left = (rect.left + Math.random() * rect.width) + 'px';
                particle.style.top = (rect.top + Math.random() * rect.height) + 'px';
                particle.style.fontSize = '20px';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '1000';
                particle.style.animation = 'particle 2s ease-out forwards';

                document.body.appendChild(particle);

                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 2000);
            }
        }

        function endGame() {
            gameState.isActive = false;
            gameState.isCompleted = true;

            document.getElementById('game-message').textContent = `Game Over! Final Score: ${gameState.score}`;
            document.getElementById('final-score').textContent = gameState.score;

            // Disable all color buttons
            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.disabled = true;
                btn.classList.add('cursor-not-allowed', 'opacity-70');
            });

            // Show completion message
            setTimeout(() => {
                document.getElementById('completion-message').classList.remove('hidden');
            }, 2000);
        }
    </script>
</body>
</html>
